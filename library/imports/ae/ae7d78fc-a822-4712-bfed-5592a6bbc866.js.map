{"version": 3, "sources": ["assets/scripts/game/GamePageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,iDAAgD;AAChD,uEAAkE;AAClE,qDAAoD;AACpD,yCAAwC;AACxC,uCAAsC;AACtC,uEAAkE;AAClE,6DAAwD;AACxD,qEAAgE;AAChE,4DAA2D;AAC3D,8CAA6C;AAEvC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAgD,sCAAY;IAA5D;QAAA,qEAojCC;QAjjCG,kBAAY,GAAY,IAAI,CAAA,CAAC,MAAM;QAGnC,eAAS,GAAa,IAAI,CAAA,CAAC,UAAU;QAGrC,oBAAc,GAAa,IAAI,CAAA,CAAC,WAAW;QAG3C,mBAAa,GAAY,IAAI,CAAA,CAAC,uBAAuB;QAGrD,gBAAU,GAAY,IAAI,CAAA,CAAC,wBAAwB;QAGnD,2BAAqB,GAA0B,IAAI,CAAA,CAAC,SAAS;QAG7D,8BAAwB,GAA6B,IAAI,CAAA,CAAC,MAAM;QAGhE,yBAAmB,GAAwB,IAAI,CAAA,CAAC,OAAO;QAGvD,0BAAoB,GAAyB,IAAI,CAAA,CAAC,OAAO;QAEzD,2BAAqB,GAAY,KAAK,CAAC,CAAE,aAAa;QACtD,sBAAgB,GAAY,KAAK,CAAC,CAAE,WAAW;QAE/C,UAAU;QACF,uBAAiB,GAAW,IAAI,CAAC,CAAC,WAAW;QAC7C,sBAAgB,GAAW,CAAC,CAAC,CAAC,UAAU;QACxC,wBAAkB,GAAW,CAAC,CAAC,CAAC,SAAS;QAEjD,SAAS;QACD,gBAAU,GAAY,KAAK,CAAC,CAAC,kDAAkD;QAC/E,gBAAU,GAAW,CAAC,CAAC,CAAC,OAAO;QAC/B,0BAAoB,GAAY,KAAK,CAAC,CAAC,aAAa;QAE5D,OAAO;QACC,oBAAc,GAAW,CAAC,CAAC,CAAC,wBAAwB;QACpD,sBAAgB,GAAW,CAAC,CAAC,CAAC,SAAS;;QAugC/C,iBAAiB;IACrB,CAAC;IArgCG,mCAAM,GAAN;QACI,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,uBAAuB;YACvB,IAAM,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC7C,IAAI,UAAU,EAAE;gBACZ,IAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;gBAC9D,IAAI,aAAa,EAAE;oBACf,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;iBACzD;aACJ;SACJ;QAED,qCAAqC;QACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,6BAA6B;YAC7B,IAAM,eAAe,GAAG,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACxD,IAAI,eAAe,EAAE;gBACjB,IAAM,kBAAkB,GAAG,eAAe,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;gBAC9E,IAAI,kBAAkB,EAAE;oBACpB,IAAI,CAAC,cAAc,GAAG,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;iBACnE;aACJ;SACJ;IAGL,CAAC;IAGS,kCAAK,GAAf;QAAA,iBAYC;QAXE,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,eAAM,CAAC,SAAS,GAAG,sBAAsB,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE;YACtH,KAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;YACjC,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAC;gBAChC,KAAI,CAAC,qBAAqB,GAAG,KAAK,CAAA;YACpC,CAAC,CAAC,CAAA;QACL,CAAC,CAAC,CAAC;QAEV,WAAW;QACX,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;SACxF;IACL,CAAC;IAED;;;OAGG;IACK,8CAAiB,GAAzB,UAA0B,KAAU;QAC1B,IAAA,KAAmB,KAAK,CAAC,MAAM,IAAI,KAAK,EAAtC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,MAAM,YAA0B,CAAC;QAE/C,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;YAEtB,OAAO;SACV;QAED,eAAe;QACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAE3B,OAAO;SACV;QAED,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAElC,iBAAiB;QACjB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,MAAM,KAAK,CAAC,EAAE;gBACd,kBAAkB;gBAClB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aAC5D;iBAAM,IAAI,MAAM,KAAK,CAAC,EAAE;gBACrB,iBAAiB;gBACjB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;aAC3D;SACJ;QAED,oBAAoB;QACpB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAErC,CAAC;IAGD,IAAI;IACJ,8CAAiB,GAAjB,UAAkB,gBAAkC;QAApD,iBAeC;QAbG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;QAElC,kBAAkB;QAClB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAA;SACpC;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAC5B,QAAQ;QACR,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACjD,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAA;QACjC,CAAC,CAAC,CAAA;IAEN,CAAC;IAES,sCAAS,GAAnB;QACI,kBAAkB;QAClB,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAA;SACpC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAA;SACvC;QAED,QAAQ;QACR,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAGD,IAAI;IACJ,wCAAW,GAAX,UAAY,gBAAkC;QAE1C,IAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,KAAK,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAlE,CAAkE,CAAC,CAAC,CAAA,KAAK;QAClI,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE,2CAA2C;YACzD,IAAI,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,YAAY;gBACxD,2BAAY,CAAC,QAAQ,EAAE,CAAC;aAC3B;iBAAM;gBACH,2BAAY,CAAC,SAAS,EAAE,CAAC;aAC5B;SACJ;aAAM;YACH,2BAAY,CAAC,QAAQ,EAAE,CAAC;SAC3B;IAEL,CAAC;IAED,uBAAuB;IACvB,wCAAW,GAAX,UAAY,IAAqB;QAG7B,SAAS;QACT,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;QAExC,eAAe;QACf,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;YACtC,OAAO;YACP,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;SAC1D;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;YAClD,4BAA4B;YAC5B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;SACvF;aAAM;YACH,MAAM;YACN,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;SAC9B;QAED,UAAU;QACV,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEnD,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3C,wBAAwB;QACxB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,CAAC;SAElD;IAGL,CAAC;IAED,aAAa;IACb,+CAAkB,GAAlB,UAAmB,IAAsB;QAGrC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QAEvC,eAAe;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,gBAAgB;QAChB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,CAAC;SACnD;QAID,QAAQ;QACR,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC;IAED,aAAa;IACb,kDAAqB,GAArB,UAAsB,IAAyB;QAA/C,iBAyBC;QAvBG,eAAe;QACf,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QAEvC,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,gBAAgB;QAChB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEtE,6BAA6B;QAG7B,qBAAqB;QACrB,UAAU,CAAC;YAEP,KAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACK,+CAAkB,GAA1B,UAA2B,IAAyB;QAEhD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACK,oDAAuB,GAA/B,UAAgC,IAAyB;QAGrD,eAAe;QAHnB,iBAwCC;QAnCG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE;YACpC,iBAAiB;YAGjB,kBAAkB;YAClB,wBAAwB;YACxB,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAU,CAAC;YAE7C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC7B,IAAM,WAAW,GAAM,MAAM,CAAC,CAAC,SAAI,MAAM,CAAC,CAAG,CAAC;gBAE9C,iBAAiB;gBACjB,IAAI,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;oBACrC,OAAO;iBACV;gBAED,cAAc;gBACd,IAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAA,CAAC;oBACnD,OAAA,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;gBAApC,CAAoC,CACvC,CAAC;gBAEF,cAAc;gBACd,KAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;gBAEpE,YAAY;gBACZ,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;YAEH,eAAe;YACf,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAA,SAAS;oBACnC,KAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,2CAAc,GAAtB,UAAuB,aAAoC,EAAE,UAAsB;QAE/E,mBAAmB;QACnB,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE;YAElD,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACK,kDAAqB,GAA7B,UAA8B,CAAS,EAAE,CAAS,EAAE,OAA8B;;QAC9E,WAAW;QACX,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C,qCAAqC;QACrC,IAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,UAAA,MAAM;YACvC,OAAA,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM;QAA/C,CAA+C,CAClD,CAAC;QAEF,IAAI,eAAe,EAAE;YACjB,0BAA0B;YAC1B,gBAAgB;YAChB,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;YAC3E,IAAM,aAAa,GAAG,eAAe,CAAC,MAAM,KAAK,aAAa,CAAC;YAC/D,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;YAChE,OAAO;SACV;QAED,2BAA2B;QAC3B,IAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAElC,IAAI,MAAM,KAAK,cAAc,EAAE;YAC3B,mBAAmB;YACnB,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACtD;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YACnC,uBAAuB;YACvB,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;;OAGG;IACK,sDAAyB,GAAjC,UAAkC,MAA2B;;QACzD,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;QACnB,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE7B,WAAW;QACX,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C,eAAe;QACf,IAAI,MAAM,KAAK,MAAM,EAAE;YACnB,eAAe;YACf,gBAAgB;YAChB,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;YAC3E,IAAM,aAAa,GAAG,MAAM,CAAC,MAAM,KAAK,aAAa,CAAC;YACtD,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;SACnE;aAAM,IAAI,MAAM,KAAK,cAAc,EAAE;YAClC,mBAAmB;YACnB,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACtD;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YACnC,uBAAuB;YACvB,IAAI,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;;OAGG;IACK,mDAAsB,GAA9B,UAA+B,SAA0B;QAAzD,iBAUC;QAPG,gBAAgB;QAChB,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YAC1C,gBAAgB;YAChB,KAAI,CAAC,YAAY,CAAC;gBACd,KAAI,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;YAChG,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa;QAClC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,aAAa;IACb,6CAAgB,GAAhB,UAAiB,IAAoB;QAEjC,iBAAiB;QACjB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QAEvC,gCAAgC;QAEhC,gBAAgB;QAChB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEtD,0BAA0B;YAC1B,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACxD;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,CAAC;SAEnD;IACL,CAAC;IAED;;;;OAIG;IACK,iDAAoB,GAA5B,UAA6B,aAAoC,EAAE,iBAA8C;QAAjH,iBAmDC;;QAlDG,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAE5E,OAAO;SACV;QAED,WAAW;QACX,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;QAC3E,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,6BAA6B;QAC7B,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,gDAAgD,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;SAC3F;QAED,8BAA8B;QAC9B,IAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,MAAM,KAAK,aAAa,EAA/B,CAA+B,CAAC,CAAC;QAC/E,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,QAAQ,EAAE;YAExC,qBAAqB,GAAG,IAAI,CAAC;YAE7B,SAAS;YACT,IAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAChE,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACjF;QAED,sBAAsB;QACtB,IAAM,mBAAmB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,MAAM,KAAK,aAAa,EAA/B,CAA+B,CAAC,CAAC;QAI5F,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YAElC,OAAO;SACV;QAED,eAAe;QACf,IAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;QAExE,aAAa;QACb,cAAc,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,WAAW;YAClC,IAAA,KAAS,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAA1C,CAAC,QAAA,EAAE,CAAC,QAAsC,CAAC;YAGlD,KAAI,CAAC,oBAAoB,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,mDAAsB,GAA9B,UAA+B,aAAoC;QAC/D,IAAM,MAAM,GAAG,IAAI,GAAG,EAAiC,CAAC;QAExD,KAAqB,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE;YAA/B,IAAM,MAAM,sBAAA;YACb,IAAM,WAAW,GAAM,MAAM,CAAC,CAAC,SAAI,MAAM,CAAC,CAAG,CAAC;YAE9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;gBAC1B,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aAC/B;YAED,MAAM,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACK,yDAA4B,GAApC,UAAqC,aAAkC;QAAvE,iBAkBC;;QAhBG,WAAW;QACX,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;QAC3E,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,cAAc;QACd,aAAa,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,KAAK;YAGhC,aAAa;YACb,KAAI,CAAC,YAAY,CAAC;gBACd,KAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YACzD,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,qDAAwB,GAAhC,UAAiC,MAAyB,EAAE,aAAqB;QAC7E,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,KAAK,aAAa,CAAC;QAEjD,IAAI,QAAQ,EAAE;YACV,oCAAoC;YACpC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SACrC;aAAM;YACH,iCAAiC;YACjC,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACK,iDAAoB,GAA5B,UAA6B,MAAyB;QAGlD,qBAAqB;QACrB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAC5F;QAED,2BAA2B;QAC3B,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;IAC3F,CAAC;IAED;;;OAGG;IACK,0DAA6B,GAArC,UAAsC,MAAyB;QAG3D,IAAI,MAAM,CAAC,aAAa,EAAE;YACtB,wCAAwC;YACxC,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aAC5F;YAED,0CAA0C;YAC1C,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;SACnE;aAAM;YACH,mBAAmB;YACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aAC5F;YAED,2BAA2B;YAC3B,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAC3E;IACL,CAAC;IAED;;;;;OAKG;IACK,2DAA8B,GAAtC,UAAuC,MAAc,EAAE,KAAa,EAAE,aAAsB;QACxF,wCAAwC;QACxC,qBAAqB;QAGrB,sCAAsC;QACtC,oDAAoD;IACxD,CAAC;IAED;;;;OAIG;IACK,0DAA6B,GAArC,UAAsC,MAAc,EAAE,KAAa;QAAnE,iBAiBC;QAdG,aAAa;QACb,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,WAAW;QACX,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,SAAS;QACT,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,mDAAsB,GAA9B,UAA+B,MAAc,EAAE,UAAkB;QAG7D,oBAAoB;QACpB,+BAA+B;IACnC,CAAC;IAED;;;OAGG;IACK,wDAA2B,GAAnC,UAAoC,aAAkC;;QAClE,WAAW;QACX,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;QAC3E,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,eAAe;QACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAE3B,OAAO;SACV;QAID,WAAW;QACX,IAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,MAAM,KAAK,aAAa,EAA/B,CAA+B,CAAC,CAAC;QAC/E,IAAI,CAAC,QAAQ,EAAE;YAEX,OAAO;SACV;QAID,eAAe;QACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,iBAAiB;YACjB,IAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAEhE,YAAY;YACZ,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAGjF;IACL,CAAC;IAED,WAAW;IACX,2CAAc,GAAd,UAAe,CAAS,EAAE,CAAS,EAAE,MAAc;QAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAElB,OAAO;SACV;QAED,eAAe;QACf,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAE3B,OAAO;SACV;QAED,IAAM,SAAS,GAAsB;YACjC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,MAAM,CAAC,qBAAqB;SACvC,CAAC;QAGF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QAE/E,oBAAoB;QACpB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAErC,CAAC;IAED,WAAW;IACX,yCAAY,GAAZ;QACI,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;IACzD,CAAC;IAED;;;OAGG;IACH,qDAAwB,GAAxB,UAAyB,IAA4B;;QAGjD,yCAAyC;QACzC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;SAC3D;QAED,kDAAkD;QAClD,IAAI,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;QACzE,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC;QAE/C,IAAI,QAAQ,EAAE;YACV,oCAAoC;YACpC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SAE5D;IACL,CAAC;IAED;;;;OAIG;IACK,kDAAqB,GAA7B,UAA8B,MAAc,EAAE,UAAkB;QAG5D,+BAA+B;QAC/B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SAErE;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;SACnE;IACL,CAAC;IAED,WAAW;IACX,8CAAiB,GAAjB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,WAAW;IACX,gDAAmB,GAAnB;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,mBAAmB;IACnB,kDAAqB,GAArB;QACI,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,kBAAkB;YACpC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,oBAAoB;SACzC,CAAC;IACN,CAAC;IAED,QAAQ;IACA,2CAAc,GAAtB,UAAuB,OAAe;QAAtC,iBAgBC;QAfG,WAAW;QACX,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,gBAAgB,GAAG,OAAO,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QAE9C,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;YACjC,gBAAgB,EAAE,CAAC;YACnB,KAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;YAE9C,IAAI,gBAAgB,IAAI,CAAC,EAAE;gBACvB,KAAI,CAAC,mBAAmB,EAAE,CAAC;aAE9B;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED,UAAU;IACF,mDAAsB,GAA9B,UAA+B,OAAe;QAC1C,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,OAAO,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAG,OAAS,CAAC,CAAE,sBAAsB;aAChE;iBAAM;gBACH,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,CAAY,YAAY;aACtD;SACJ;QACD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;IACpC,CAAC;IAED,UAAU;IACF,mDAAsB,GAA9B,UAA+B,SAAiB;QAC5C,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,KAAG,SAAW,CAAC;SAC/C;IACL,CAAC;IAED,eAAe;IACP,6CAAgB,GAAxB,UAAyB,OAAe;QACpC,UAAU;QACV,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,gBAAgB;QAChB,IAAI,OAAO,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;aAAM,IAAI,OAAO,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;SACrB;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,iDAAY,OAAO,2DAAW,CAAC,CAAC;YAC7C,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAED,SAAS;IACD,0CAAa,GAArB;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SAEpC;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC7B;IACL,CAAC;IAED,UAAU;IACF,uCAAU,GAAlB;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;SAEjC;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC9B;IACL,CAAC;IAED,SAAS;IACD,wCAAW,GAAnB;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;SACrC;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;SAClC;IACL,CAAC;IAED,WAAW;IACH,gDAAmB,GAA3B;QACI,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;IACL,CAAC;IAED;;;;OAIG;IACK,6EAAgD,GAAxD,UAAyD,aAAoC,EAAE,iBAA6C;QAA5I,iBA2CC;;QA1CG,WAAW;QACX,IAAM,aAAa,eAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,CAAC;QAC3E,IAAI,CAAC,aAAa,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,SAAS;QACT,IAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,MAAM,CAAC,aAAa,EAApB,CAAoB,CAAC,CAAC;QAC7E,IAAM,wBAAwB,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,KAAK,aAAa,CAAC;QAIjG,0BAA0B;QAC1B,IAAI,CAAC,wBAAwB,IAAI,iBAAiB,EAAE;YAChD,IAAM,sBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,sBAAoB,KAAK,CAAC,CAAC,EAAE;gBAG7B,cAAc;gBACd,IAAI,CAAC,YAAY,CAAC;oBACd,KAAI,CAAC,qBAAqB,CAAC,sBAAoB,EAAE,CAAC,CAAC,CAAC;gBACxD,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;SACJ;QAED,mBAAmB;QACnB,aAAa,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,KAAK;YAChC,IAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzD,IAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAE3C,aAAa;YACb,KAAI,CAAC,YAAY,CAAC;gBACd,IAAI,aAAa,EAAE;oBACf,4BAA4B;oBAC5B,KAAI,CAAC,mCAAmC,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;iBAC/E;qBAAM;oBACH,kBAAkB;oBAClB,KAAI,CAAC,sCAAsC,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;iBAClF;YACL,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACK,mEAAsC,GAA9C,UAA+C,MAA2B,EAAE,aAAqB,EAAE,UAAkB;QAArH,iBA6BC;QA5BG,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,KAAK,aAAa,CAAC;QAEjD,6BAA6B;QAC7B,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,SAAS;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpD,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBAClB,eAAe;gBACf,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;aACvD;SACJ;QAED,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,KAAI,CAAC,mBAAmB,EAAE;gBAE1B,KAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAEtE,aAAa;gBACb,KAAI,CAAC,kCAAkC,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aACtE;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAE9D,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,+DAAkC,GAA1C,UAA2C,MAAc,EAAE,UAAkB;QACzE,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO;SACV;QAED,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QACvE,IAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,MAAM,EAAtB,CAAsB,CAAC,CAAC;QAElE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;YAClB,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC;SAEvC;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,4CAAiB,MAAQ,CAAC,CAAC;SAC3C;IACL,CAAC;IAED;;;;OAIG;IACK,0CAAa,GAArB,UAAsB,MAAc;QAChC,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO,CAAC,CAAC,CAAC;SACb;QAED,IAAI,KAAK,GAAe,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;QACvE,OAAO,KAAK,CAAC,SAAS,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,MAAM,KAAK,MAAM,EAAtB,CAAsB,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACK,oDAAuB,GAA/B,UAAgC,MAAc,EAAE,KAAa;QAGzD,+BAA+B;QAC/B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAChE;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;SACrD;IACL,CAAC;IAED;;;;OAIG;IACK,kDAAqB,GAA7B,UAA8B,SAAiB,EAAE,KAAa;QAC1D,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACpD,OAAO;SACV;QAED,6BAA6B;QAC7B,IAAM,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAC3F,IAAI,qBAAqB,EAAE;YACvB,UAAU;YACV,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,qBAAqB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aAC7C;iBAAM,IAAI,KAAK,GAAG,CAAC,EAAE;gBAClB,qBAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;aACvD;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,gDAAW,SAAS,6CAA2B,CAAC,CAAC;SACjE;IACL,CAAC;IAED;;;;;OAKG;IACK,gEAAmC,GAA3C,UAA4C,MAA2B,EAAE,aAAqB,EAAE,UAAkB;QAAlH,iBAoBC;QAnBG,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEpD,mCAAmC;QACnC,IAAI,CAAC,YAAY,CAAC;YAGd,2BAA2B;YAC3B,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBAClB,KAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;aACvD;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,KAAI,CAAC,mBAAmB,EAAE;gBAC1B,KAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACtE,KAAI,CAAC,kCAAkC,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aACtE;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,sCAAS,GAAT;QACI,aAAa;QACb,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;SACzF;IACL,CAAC;IA9iCD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACU;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;yDACO;IAG1B;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;8DACY;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACQ;IAG1B;QADC,QAAQ,CAAC,+BAAqB,CAAC;qEACmB;IAGnD;QADC,QAAQ,CAAC,kCAAwB,CAAC;wEACsB;IAGzD;QADC,QAAQ,CAAC,6BAAmB,CAAC;mEACiB;IAG/C;QADC,QAAQ,CAAC,8BAAoB,CAAC;oEACkB;IA3BhC,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CAojCtC;IAAD,yBAAC;CApjCD,AAojCC,CApjC+C,EAAE,CAAC,SAAS,GAojC3D;kBApjCoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { NoticeSettlement, NoticeRoundStart, NoticeActionDisplay, PlayerActionDisplay, NoticeRoundEnd, PlayerRoundResult, ClickBlockRequest, NoticeStartGame, NoticeFirstChoiceBonus, FloodFillResult, RoomUser } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\nimport LeaveDialogController from \"../hall/LeaveDialogController\";\nimport { AudioManager } from \"../util/AudioManager\";\nimport { Config } from \"../util/Config\";\nimport { Tools } from \"../util/Tools\";\nimport CongratsDialogController from \"./CongratsDialogController\";\nimport GameScoreController from \"./GameScoreController\";\nimport ChessBoardController from \"./Chess/ChessBoardController\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class GamePageController extends cc.Component {\n\n    @property(cc.Node)\n    boardBtnBack: cc.Node = null //返回按钮\n\n    @property(cc.Label)\n    timeLabel: cc.Label = null // 计时器显示标签\n\n    @property(cc.Label)\n    mineCountLabel: cc.Label = null // 炸弹数量显示标签\n\n    @property(cc.Node)\n    squareMapNode: cc.Node = null // 方形地图节点 (mapType = 0)\n\n    @property(cc.Node)\n    hexMapNode: cc.Node = null // 六边形地图节点 (mapType = 1)\n\n    @property(LeaveDialogController)\n    leaveDialogController: LeaveDialogController = null // 退出游戏弹窗\n\n    @property(CongratsDialogController)\n    congratsDialogController: CongratsDialogController = null //结算弹窗\n\n    @property(GameScoreController)\n    gameScoreController: GameScoreController = null //分数控制器\n\n    @property(ChessBoardController)\n    chessBoardController: ChessBoardController = null //棋盘控制器\n\n    isLeaveGameDialogShow: boolean = false;  //是否显示退出游戏的弹窗\n    isCongratsDialog: boolean = false;  //是否显示结算的弹窗\n\n    // 计时器相关属性\n    private countdownInterval: number = null; // 倒计时定时器ID\n    private currentCountdown: number = 0; // 当前倒计时秒数\n    private currentRoundNumber: number = 0; // 当前回合编号\n\n    // 游戏状态管理\n    private canOperate: boolean = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）\n    private gameStatus: number = 0; // 游戏状态\n    private hasOperatedThisRound: boolean = false; // 本回合是否已经操作过\n\n    // 游戏数据\n    private currentMapType: number = 0; // 当前地图类型 0-方形地图，1-六边形地图\n    private currentMineCount: number = 0; // 当前炸弹数量\n\n\n    onLoad() {\n        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找\n        if (!this.timeLabel) {\n            // 根据场景结构查找time_label节点\n            const timeBgNode = cc.find('Canvas/time_bg');\n            if (timeBgNode) {\n                const timeLabelNode = timeBgNode.getChildByName('time_label');\n                if (timeLabelNode) {\n                    this.timeLabel = timeLabelNode.getComponent(cc.Label);\n                }\n            }\n        }\n\n        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找\n        if (!this.mineCountLabel) {\n            // 根据场景结构查找mine_count_label节点\n            const mineCountBgNode = cc.find('Canvas/mine_count_bg');\n            if (mineCountBgNode) {\n                const mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');\n                if (mineCountLabelNode) {\n                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);\n                }\n            }\n        }\n\n\n    }\n\n\n    protected start(): void {\n       Tools.imageButtonClick(this.boardBtnBack, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {\n                  this.isLeaveGameDialogShow = true\n                  this.leaveDialogController.show(1,()=>{\n                    this.isLeaveGameDialogShow = false\n                  })\n               });\n\n        // 监听棋盘点击事件\n        if (this.chessBoardController) {\n            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);\n        }\n    }\n\n    /**\n     * 处理棋盘点击事件\n     * @param event 事件数据 {x: number, y: number, action: number}\n     */\n    private onChessBoardClick(event: any) {\n        const { x, y, action } = event.detail || event;\n\n        // 检查是否可以操作（在操作时间内）\n        if (!this.isCanOperate()) {\n          \n            return;\n        }\n\n        // 检查本回合是否已经操作过\n        if (this.hasOperatedThisRound) {\n        \n            return;\n        }\n\n        // 发送点击操作\n        this.sendClickBlock(x, y, action);\n\n        // 操作有效，通知棋盘生成预制体\n        if (this.chessBoardController) {\n            if (action === 1) {\n                // 挖掘操作，生成不带旗子的预制体\n                this.chessBoardController.placePlayerOnGrid(x, y, false);\n            } else if (action === 2) {\n                // 标记操作，生成带旗子的预制体\n                this.chessBoardController.placePlayerOnGrid(x, y, true);\n            }\n        }\n\n        // 标记本回合已经操作过，禁止后续交互\n        this.hasOperatedThisRound = true;\n    \n    }\n\n\n    //结算\n    setCongratsDialog(noticeSettlement: NoticeSettlement) {\n\n        this.setCongrats(noticeSettlement)\n\n        //退出弹窗正在显示的话  就先关闭\n        if (this.isLeaveGameDialogShow) {\n            this.leaveDialogController.hide()\n        }\n\n        this.isCongratsDialog = true\n        //弹出结算弹窗\n        this.congratsDialogController.show(noticeSettlement, () => {\n            this.isCongratsDialog = false\n        })\n\n    }\n\n    protected onDisable(): void {\n        //退出弹窗正在显示的话  就先关闭\n        if (this.isLeaveGameDialogShow) {\n            this.leaveDialogController.hide()\n        }\n\n        //结算弹窗正在显示的话就先关闭掉\n        if (this.isCongratsDialog) {\n            this.congratsDialogController.hide()\n        }\n\n        // 清理计时器\n        this.clearCountdownTimer();\n    }\n\n\n    //结算\n    setCongrats(noticeSettlement: NoticeSettlement) {\n\n        const index = noticeSettlement.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索 \n        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效\n            if (noticeSettlement.users[index].rank === 1) { //判断自己是不是第一名\n                AudioManager.winAudio();\n            } else {\n                AudioManager.loseAudio();\n            }\n        } else {\n            AudioManager.winAudio();\n        }\n\n    }\n\n    // 处理游戏开始通知，获取炸弹数量和地图类型\n    onGameStart(data: NoticeStartGame) {\n      \n\n        // 保存地图类型\n        this.currentMapType = data.mapType || 0;\n\n        // 根据地图类型获取炸弹数量\n        if (data.mapType === 0 && data.mapConfig) {\n            // 方形地图\n            this.currentMineCount = data.mapConfig.mineCount || 13;\n        } else if (data.mapType === 1 && data.validHexCoords) {\n            // 六边形地图，暂时使用默认值，后续可根据实际需求调整\n            this.currentMineCount = Math.floor(data.validHexCoords.length * 0.15); // 约15%的格子是炸弹\n        } else {\n            // 默认值\n            this.currentMineCount = 13;\n        }\n\n        // 更新炸弹数UI\n        this.updateMineCountDisplay(this.currentMineCount);\n\n        // 根据地图类型控制地图节点的显示与隐藏\n        this.switchMapDisplay(this.currentMapType);\n\n        // 初始化分数界面（使用后端传回来的真实数据）\n        if (this.gameScoreController) {\n            this.gameScoreController.initializeScoreView();\n           \n        }\n\n       \n    }\n\n    // 处理扫雷回合开始通知\n    onNoticeRoundStart(data: NoticeRoundStart) {\n       \n\n        this.currentRoundNumber = data.roundNumber || 1;\n        this.currentCountdown = data.countDown || 25;\n        this.gameStatus = data.gameStatus || 0;\n\n        // 新回合开始，重置操作状态\n        this.canOperate = true;\n        this.hasOperatedThisRound = false;\n\n        // 清理棋盘上的所有玩家预制体\n        if (this.chessBoardController) {\n            this.chessBoardController.clearAllPlayerNodes();\n        }\n\n       \n\n        // 开始倒计时\n        this.startCountdown(this.currentCountdown);\n    }\n\n    // 处理扫雷操作展示通知\n    onNoticeActionDisplay(data: NoticeActionDisplay) {\n        \n        // 进入展示阶段，不能再操作\n        this.canOperate = false;\n        this.gameStatus = data.gameStatus || 0;\n\n        // 根据countDown重置倒计时为5秒\n        this.currentCountdown = data.countDown || 5;\n        this.updateCountdownDisplay(this.currentCountdown);\n        this.startCountdown(this.currentCountdown);\n\n        // 在棋盘上显示所有玩家的操作\n        this.displayPlayerActions(data.playerActions, data.playerTotalScores);\n\n        // 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）\n       \n\n        // 使用setTimeout作为备选方案\n        setTimeout(() => {\n            \n            this.updateBoardAfterActions(data);\n        }, 1000);\n\n        // 同时也使用scheduleOnce\n        this.scheduleOnce(this.delayedUpdateBoard.bind(this, data), 1.0);\n    }\n\n    /**\n     * 延迟更新棋盘的回调方法\n     * @param data NoticeActionDisplay数据\n     */\n    private delayedUpdateBoard(data: NoticeActionDisplay) {\n        \n        this.updateBoardAfterActions(data);\n    }\n\n    /**\n     * 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）\n     * @param data NoticeActionDisplay数据\n     */\n    private updateBoardAfterActions(data: NoticeActionDisplay) {\n\n\n        // 第一步：先让所有头像消失\n\n        this.hideAllAvatars(data.playerActions, () => {\n            // 头像消失完成后，执行后续操作\n\n\n            // 第二步：处理每个玩家的操作结果\n            // 先按位置分组，处理同一位置有多个操作的情况\n            const processedPositions = new Set<string>();\n\n            data.playerActions.forEach(action => {\n                const positionKey = `${action.x},${action.y}`;\n\n                // 如果这个位置已经处理过，跳过\n                if (processedPositions.has(positionKey)) {\n                    return;\n                }\n\n                // 查找同一位置的所有操作\n                const samePositionActions = data.playerActions.filter(a =>\n                    a.x === action.x && a.y === action.y\n                );\n\n                // 处理同一位置的操作结果\n                this.processPositionResult(action.x, action.y, samePositionActions);\n\n                // 标记这个位置已处理\n                processedPositions.add(positionKey);\n            });\n\n            // 第三步：处理连锁展开结果\n            if (data.floodFillResults && data.floodFillResults.length > 0) {\n                data.floodFillResults.forEach(floodFill => {\n                    this.processFloodFillResult(floodFill);\n                });\n            }\n        });\n    }\n\n    /**\n     * 让所有头像消失（简化版：直接删除所有头像）\n     * @param playerActions 玩家操作列表\n     * @param onComplete 完成回调\n     */\n    private hideAllAvatars(playerActions: PlayerActionDisplay[], onComplete: () => void) {\n      \n        // 直接调用一次头像删除，不区分位置\n        this.chessBoardController.hideAvatarsAtPosition(0, 0, () => {\n   \n            onComplete();\n        });\n    }\n\n    /**\n     * 处理同一位置的多个操作结果\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param actions 该位置的所有操作\n     */\n    private processPositionResult(x: number, y: number, actions: PlayerActionDisplay[]) {\n        // 删除该位置的格子\n        this.chessBoardController.removeGridAt(x, y);\n\n        // 检查是否有地雷被点击（action=1且result=\"mine\"）\n        const mineClickAction = actions.find(action =>\n            action.action === 1 && action.result === \"mine\"\n        );\n\n        if (mineClickAction) {\n            // 如果有地雷被点击，直接显示炸弹，不管是否有标记\n            // 判断是否是当前用户点到的雷\n            const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n            const isCurrentUser = mineClickAction.userId === currentUserId;\n            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);\n            return;\n        }\n\n        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果\n        const firstAction = actions[0];\n        const result = firstAction.result;\n\n        if (result === \"correct_mark\") {\n            // 正确标记：生成biaoji预制体\n            this.chessBoardController.createBiaojiPrefab(x, y);\n        } else if (typeof result === \"number\") {\n            // 数字：更新neighborMines显示\n            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);\n        }\n    }\n\n    /**\n     * 处理单个玩家操作结果（保留原方法以防其他地方调用）\n     * @param action 玩家操作数据\n     */\n    private processPlayerActionResult(action: PlayerActionDisplay) {\n        const x = action.x;\n        const y = action.y;\n        const result = action.result;\n\n        // 删除该位置的格子\n        this.chessBoardController.removeGridAt(x, y);\n\n        // 根据结果生成相应的预制体\n        if (result === \"mine\") {\n            // 地雷：生成boom预制体\n            // 判断是否是当前用户点到的雷\n            const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n            const isCurrentUser = action.userId === currentUserId;\n            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);\n        } else if (result === \"correct_mark\") {\n            // 正确标记：生成biaoji预制体\n            this.chessBoardController.createBiaojiPrefab(x, y);\n        } else if (typeof result === \"number\") {\n            // 数字：更新neighborMines显示\n            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);\n        }\n    }\n\n    /**\n     * 处理连锁展开结果\n     * @param floodFill 连锁展开数据\n     */\n    private processFloodFillResult(floodFill: FloodFillResult) {\n       \n\n        // 为每个连锁格子播放消失动画\n        floodFill.revealedBlocks.forEach((block, index) => {\n            // 延迟播放动画，创造连锁效果\n            this.scheduleOnce(() => {\n                this.chessBoardController.playGridDisappearAnimation(block.x, block.y, block.neighborMines);\n            }, index * 0.1); // 每个格子间隔0.1秒\n        });\n    }\n\n    // 处理扫雷回合结束通知\n    onNoticeRoundEnd(data: NoticeRoundEnd) {\n\n        // 进入回合结束阶段，不能再操作\n        this.canOperate = false;\n        this.gameStatus = data.gameStatus || 1;\n\n        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321\n\n        // 处理玩家分数动画和头像显示\n        if (data.playerResults && data.playerResults.length > 0) {\n            this.displayPlayerScoreAnimations(data.playerResults);\n\n            // 如果本回合我没有操作，根据后端消息生成我的头像\n            this.handleMyAvatarIfNotOperated(data.playerResults);\n        }\n\n        // 清理棋盘上的所有玩家预制体\n        if (this.chessBoardController) {\n            this.chessBoardController.clearAllPlayerNodes();\n            \n        }\n    }\n\n    /**\n     * 在棋盘上显示所有玩家的操作\n     * @param playerActions 玩家操作列表\n     * @param playerTotalScores 玩家总分数据\n     */\n    private displayPlayerActions(playerActions: PlayerActionDisplay[], playerTotalScores?: {[userId: string]: number}) {\n        if (!this.chessBoardController || !playerActions || playerActions.length === 0) {\n            \n            return;\n        }\n\n        // 获取当前用户ID\n        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n        if (!currentUserId) {\n            console.warn(\"无法获取当前用户ID\");\n            return;\n        }\n\n        // 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）\n        if (playerTotalScores) {\n            this.displayPlayerScoreAnimationsAndUpdateTotalScores(playerActions, playerTotalScores);\n        }\n\n        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像\n        const myAction = playerActions.find(action => action.userId === currentUserId);\n        let shouldDisplayMyAvatar = false;\n\n        if (!this.hasOperatedThisRound && myAction) {\n           \n            shouldDisplayMyAvatar = true;\n\n            // 生成我的头像\n            const withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子\n            this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);\n        }\n\n        // 过滤掉自己的操作，只显示其他玩家的操作\n        const otherPlayersActions = playerActions.filter(action => action.userId !== currentUserId);\n\n       \n\n        if (otherPlayersActions.length === 0) {\n           \n            return;\n        }\n\n        // 按位置分组其他玩家的操作\n        const positionGroups = this.groupActionsByPosition(otherPlayersActions);\n\n        // 为每个位置生成预制体\n        positionGroups.forEach((actions, positionKey) => {\n            const [x, y] = positionKey.split(',').map(Number);\n          \n\n            this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);\n        });\n    }\n\n    /**\n     * 按位置分组玩家操作\n     * @param playerActions 玩家操作列表\n     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value\n     */\n    private groupActionsByPosition(playerActions: PlayerActionDisplay[]): Map<string, PlayerActionDisplay[]> {\n        const groups = new Map<string, PlayerActionDisplay[]>();\n\n        for (const action of playerActions) {\n            const positionKey = `${action.x},${action.y}`;\n\n            if (!groups.has(positionKey)) {\n                groups.set(positionKey, []);\n            }\n\n            groups.get(positionKey)!.push(action);\n        }\n\n        return groups;\n    }\n\n    /**\n     * 显示玩家分数动画\n     * @param playerResults 玩家回合结果列表\n     */\n    private displayPlayerScoreAnimations(playerResults: PlayerRoundResult[]) {\n      \n        // 获取当前用户ID\n        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n        if (!currentUserId) {\n            console.warn(\"无法获取当前用户ID\");\n            return;\n        }\n\n        // 为每个玩家显示分数动画\n        playerResults.forEach((result, index) => {\n         \n\n            // 延迟显示，让动画错开\n            this.scheduleOnce(() => {\n                this.showPlayerScoreAnimation(result, currentUserId);\n            }, index * 0.2);\n        });\n    }\n\n    /**\n     * 显示单个玩家的分数动画\n     * @param result 玩家回合结果\n     * @param currentUserId 当前用户ID\n     */\n    private showPlayerScoreAnimation(result: PlayerRoundResult, currentUserId: string) {\n        const isMyself = result.userId === currentUserId;\n\n        if (isMyself) {\n            // 自己的分数动画：在player_game_pfb里只显示本回合得分\n            this.showMyScoreAnimation(result);\n        } else {\n            // 其他人的分数动画：根据isFirstChoice决定显示逻辑\n            this.showOtherPlayerScoreAnimation(result);\n        }\n    }\n\n    /**\n     * 显示自己的分数动画\n     * @param result 玩家回合结果\n     */\n    private showMyScoreAnimation(result: PlayerRoundResult) {\n     \n\n        // 在棋盘上的头像预制体中显示本回合得分\n        if (this.chessBoardController) {\n            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);\n        }\n\n        // 在player_score_pfb中显示分数动画\n        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);\n    }\n\n    /**\n     * 显示其他玩家的分数动画\n     * @param result 玩家回合结果\n     */\n    private showOtherPlayerScoreAnimation(result: PlayerRoundResult) {\n       \n\n        if (result.isFirstChoice) {\n            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分\n            if (this.chessBoardController) {\n                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);\n            }\n\n            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分\n            this.showFirstChoiceScoreAnimation(result.userId, result.score);\n        } else {\n            // 其他人非先手：正常显示本回合得分\n            if (this.chessBoardController) {\n                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);\n            }\n\n            // 在player_score_pfb中显示分数动画\n            this.showScoreAnimationInScorePanel(result.userId, result.score, false);\n        }\n    }\n\n    /**\n     * 在分数面板中显示分数动画\n     * @param userId 用户ID\n     * @param score 本回合得分\n     * @param isFirstChoice 是否为先手\n     */\n    private showScoreAnimationInScorePanel(userId: string, score: number, isFirstChoice: boolean) {\n        // 这里需要找到对应的PlayerScoreController并调用分数动画\n        // 由于没有直接的引用，这里先用日志记录\n       \n\n        // TODO: 实现在player_score_pfb中显示分数动画的逻辑\n        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法\n    }\n\n    /**\n     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）\n     * @param userId 用户ID\n     * @param score 本回合得分\n     */\n    private showFirstChoiceScoreAnimation(userId: string, score: number) {\n       \n\n        // 先显示+1的先手奖励\n        this.scheduleOnce(() => {\n            this.showScoreAnimationInScorePanel(userId, 1, true);\n        }, 0.1);\n\n        // 再显示本回合得分\n        this.scheduleOnce(() => {\n            this.showScoreAnimationInScorePanel(userId, score, false);\n        }, 1.2);\n\n        // 最后更新总分\n        this.scheduleOnce(() => {\n            this.updatePlayerTotalScore(userId, score + 1);\n        }, 2.4);\n    }\n\n    /**\n     * 更新玩家总分\n     * @param userId 用户ID\n     * @param totalScore 新的总分\n     */\n    private updatePlayerTotalScore(userId: string, totalScore: number) {\n     \n\n        // TODO: 实现更新玩家总分的逻辑\n        // 需要更新GlobalBean中的用户数据，并刷新UI显示\n    }\n\n    /**\n     * 如果本回合我没有操作，根据后端消息生成我的头像\n     * @param playerResults 玩家回合结果列表\n     */\n    private handleMyAvatarIfNotOperated(playerResults: PlayerRoundResult[]) {\n        // 获取当前用户ID\n        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n        if (!currentUserId) {\n            console.warn(\"无法获取当前用户ID\");\n            return;\n        }\n\n        // 检查本回合是否进行了操作\n        if (this.hasOperatedThisRound) {\n           \n            return;\n        }\n\n      \n\n        // 查找我的操作结果\n        const myResult = playerResults.find(result => result.userId === currentUserId);\n        if (!myResult) {\n           \n            return;\n        }\n\n       \n\n        // 根据后端消息生成我的头像\n        if (this.chessBoardController) {\n            // 根据操作类型决定是否显示旗子\n            const withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子\n\n            // 生成我的头像预制体\n            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);\n\n           \n        }\n    }\n\n    // 发送点击方块消息\n    sendClickBlock(x: number, y: number, action: number) {\n        if (!this.canOperate) {\n          \n            return;\n        }\n\n        // 检查本回合是否已经操作过\n        if (this.hasOperatedThisRound) {\n           \n            return;\n        }\n\n        const clickData: ClickBlockRequest = {\n            x: x,\n            y: y,\n            action: action // 1=挖掘方块，2=标记/取消标记地雷\n        };\n\n       \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeClickBlock, clickData);\n\n        // 标记本回合已经操作过，防止重复操作\n        this.hasOperatedThisRound = true;\n       \n    }\n\n    // 检查是否可以操作\n    isCanOperate(): boolean {\n        return this.canOperate && !this.hasOperatedThisRound;\n    }\n\n    /**\n     * 处理首选玩家奖励通知\n     * @param data NoticeFirstChoiceBonus 消息数据\n     */\n    onNoticeFirstChoiceBonus(data: NoticeFirstChoiceBonus) {\n      \n\n        // 转发给GameScoreController处理所有玩家的分数更新和加分动画\n        if (this.gameScoreController) {\n            this.gameScoreController.onNoticeFirstChoiceBonus(data);\n        }\n\n        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score\n        let currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n        let isMyself = (data.userId === currentUserId);\n\n        if (isMyself) {\n            // 更新player_game_pfb中的change_score显示\n            this.updatePlayerGameScore(data.userId, data.bonusScore);\n          \n        }\n    }\n\n    /**\n     * 更新player_game_pfb中的change_score显示\n     * @param userId 用户ID\n     * @param bonusScore 奖励分数\n     */\n    private updatePlayerGameScore(userId: string, bonusScore: number) {\n       \n\n        // 调用ChessBoardController显示加分效果\n        if (this.chessBoardController) {\n            this.chessBoardController.showPlayerGameScore(userId, bonusScore);\n           \n        } else {\n            console.warn(\"ChessBoardController未设置，无法显示player_game_pfb加分效果\");\n        }\n    }\n\n    // 获取当前地图类型\n    getCurrentMapType(): number {\n        return this.currentMapType;\n    }\n\n    // 获取当前炸弹数量\n    getCurrentMineCount(): number {\n        return this.currentMineCount;\n    }\n\n    // 获取当前回合操作状态（用于调试）\n    getCurrentRoundStatus(): {roundNumber: number, canOperate: boolean, hasOperated: boolean} {\n        return {\n            roundNumber: this.currentRoundNumber,\n            canOperate: this.canOperate,\n            hasOperated: this.hasOperatedThisRound\n        };\n    }\n\n    // 开始倒计时\n    private startCountdown(seconds: number) {\n        // 清除之前的计时器\n        this.clearCountdownTimer();\n\n        let remainingSeconds = seconds;\n        this.updateCountdownDisplay(remainingSeconds);\n\n        this.countdownInterval = setInterval(() => {\n            remainingSeconds--;\n            this.updateCountdownDisplay(remainingSeconds);\n\n            if (remainingSeconds <= 0) {\n                this.clearCountdownTimer();\n               \n            }\n        }, 1000);\n    }\n\n    // 更新倒计时显示\n    private updateCountdownDisplay(seconds: number) {\n        if (this.timeLabel) {\n            if (seconds > 0) {\n                this.timeLabel.string = `${seconds}`;  // 显示纯数字：5, 4, 3, 2, 1\n            } else {\n                this.timeLabel.string = \"\";            // 不显示0，显示空白\n            }\n        }\n        this.currentCountdown = seconds;\n    }\n\n    // 更新炸弹数显示\n    private updateMineCountDisplay(mineCount: number) {\n        if (this.mineCountLabel) {\n            this.mineCountLabel.string = `${mineCount}`;\n        }\n    }\n\n    // 根据地图类型切换地图显示\n    private switchMapDisplay(mapType: number) {\n        // 先隐藏所有地图\n        this.hideAllMaps();\n\n        // 根据地图类型显示对应的地图\n        if (mapType === 0) {\n            this.showSquareMap();\n        } else if (mapType === 1) {\n            this.showHexMap();\n        } else {\n            console.warn(`未知的地图类型: ${mapType}，默认显示方形地图`);\n            this.showSquareMap();\n        }\n    }\n\n    // 显示方形地图\n    private showSquareMap() {\n        if (this.squareMapNode) {\n            this.squareMapNode.active = true;\n           \n        } else {\n            console.warn('方形地图节点未挂载');\n        }\n    }\n\n    // 显示六边形地图\n    private showHexMap() {\n        if (this.hexMapNode) {\n            this.hexMapNode.active = true;\n           \n        } else {\n            console.warn('六边形地图节点未挂载');\n        }\n    }\n\n    // 隐藏所有地图\n    private hideAllMaps() {\n        if (this.squareMapNode) {\n            this.squareMapNode.active = false;\n        }\n        if (this.hexMapNode) {\n            this.hexMapNode.active = false;\n        }\n    }\n\n    // 清除倒计时定时器\n    private clearCountdownTimer() {\n        if (this.countdownInterval) {\n            clearInterval(this.countdownInterval);\n            this.countdownInterval = null;\n        }\n    }\n\n    /**\n     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）\n     * @param playerActions 玩家操作列表\n     * @param playerTotalScores 玩家总分数据\n     */\n    private displayPlayerScoreAnimationsAndUpdateTotalScores(playerActions: PlayerActionDisplay[], playerTotalScores: {[userId: string]: number}) {\n        // 获取当前用户ID\n        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;\n        if (!currentUserId) {\n            console.warn(\"无法获取当前用户ID\");\n            return;\n        }\n\n        // 查找先手玩家\n        const firstChoicePlayer = playerActions.find(action => action.isFirstChoice);\n        const isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;\n\n      \n\n        // 如果我不是先手，先为先手玩家在分数面板显示+1\n        if (!isCurrentUserFirstChoice && firstChoicePlayer) {\n            const firstChoiceUserIndex = this.findUserIndex(firstChoicePlayer.userId);\n            if (firstChoiceUserIndex !== -1) {\n              \n\n                // 0.1秒后显示先手+1\n                this.scheduleOnce(() => {\n                    this.showScoreInScorePanel(firstChoiceUserIndex, 1);\n                }, 0.1);\n            }\n        }\n\n        // 为每个玩家显示分数动画和更新总分\n        playerActions.forEach((action, index) => {\n            const totalScore = playerTotalScores[action.userId] || 0;\n            const isFirstChoice = action.isFirstChoice;\n\n            // 延迟显示，让动画错开\n            this.scheduleOnce(() => {\n                if (isFirstChoice) {\n                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）\n                    this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);\n                } else {\n                    // 非先手玩家：直接显示本回合分数\n                    this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);\n                }\n            }, index * 0.2);\n        });\n    }\n\n    /**\n     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）\n     * @param action 玩家操作数据\n     * @param currentUserId 当前用户ID\n     * @param totalScore 玩家总分\n     */\n    private showPlayerScoreAnimationAndUpdateTotal(action: PlayerActionDisplay, currentUserId: string, totalScore: number) {\n        const isMyself = action.userId === currentUserId;\n\n        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）\n        if (this.gameScoreController) {\n            // 找到用户索引\n            const userIndex = this.findUserIndex(action.userId);\n            if (userIndex !== -1) {\n                // 在分数面板显示加减分效果\n                this.showScoreInScorePanel(userIndex, action.score);\n            }\n        }\n\n        // 2. 更新总分（参考先手加分的updatePlayerScore）\n        this.scheduleOnce(() => {\n            if (this.gameScoreController) {\n               \n                this.gameScoreController.updatePlayerScore(action.userId, totalScore);\n\n                // 更新全局数据中的总分\n                this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);\n            }\n        }, 1.2);\n\n        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）\n        this.scheduleOnce(() => {\n            this.showScoreOnPlayerAvatar(action.userId, action.score);\n            \n        }, 0.1);\n    }\n\n    /**\n     * 更新全局数据中的玩家总分\n     * @param userId 用户ID\n     * @param totalScore 新的总分\n     */\n    private updatePlayerTotalScoreInGlobalData(userId: string, totalScore: number) {\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法更新玩家总分\");\n            return;\n        }\n\n        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;\n        const userIndex = users.findIndex(user => user.userId === userId);\n\n        if (userIndex !== -1) {\n            users[userIndex].score = totalScore;\n            \n        } else {\n            console.warn(`找不到玩家: userId=${userId}`);\n        }\n    }\n\n    /**\n     * 查找用户索引\n     * @param userId 用户ID\n     * @returns 用户索引，找不到返回-1\n     */\n    private findUserIndex(userId: string): number {\n        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n            console.warn(\"没有游戏数据，无法查找用户索引\");\n            return -1;\n        }\n\n        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;\n        return users.findIndex(user => user.userId === userId);\n    }\n\n    /**\n     * 在玩家头像上显示加减分\n     * @param userId 用户ID\n     * @param score 分数变化\n     */\n    private showScoreOnPlayerAvatar(userId: string, score: number) {\n        \n\n        // 调用ChessBoardController显示加分效果\n        if (this.chessBoardController) {\n            this.chessBoardController.showPlayerGameScore(userId, score);\n        } else {\n            console.warn(\"chessBoardController 不存在，无法显示头像分数\");\n        }\n    }\n\n    /**\n     * 在分数面板显示加减分效果\n     * @param userIndex 用户索引\n     * @param score 分数变化\n     */\n    private showScoreInScorePanel(userIndex: number, score: number) {\n        if (!this.gameScoreController) {\n            console.warn(\"gameScoreController 不存在，无法在分数面板显示分数\");\n            return;\n        }\n\n        // 获取对应的PlayerScoreController\n        const playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);\n        if (playerScoreController) {\n            // 显示加减分效果\n            if (score > 0) {\n                playerScoreController.showAddScore(score);\n            } else if (score < 0) {\n                playerScoreController.showSubScore(Math.abs(score));\n            }\n        } else {\n            console.warn(`找不到用户索引 ${userIndex} 对应的PlayerScoreController`);\n        }\n    }\n\n    /**\n     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）\n     * @param action 玩家操作数据\n     * @param currentUserId 当前用户ID\n     * @param totalScore 玩家总分\n     */\n    private showFirstChoicePlayerScoreAnimation(action: PlayerActionDisplay, currentUserId: string, totalScore: number) {\n        const userIndex = this.findUserIndex(action.userId);\n\n        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）\n        this.scheduleOnce(() => {\n   \n\n            // 分数面板显示本回合分数（+1已经在前面显示过了）\n            if (userIndex !== -1) {\n                this.showScoreInScorePanel(userIndex, action.score);\n            }\n        }, 1.2);\n\n        // 第二步：更新总分（2.4秒）\n        this.scheduleOnce(() => {\n            if (this.gameScoreController) {\n                this.gameScoreController.updatePlayerScore(action.userId, totalScore);\n                this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);\n            }\n        }, 2.4);\n    }\n\n    onDestroy() {\n        // 移除棋盘点击事件监听\n        if (this.chessBoardController) {\n            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);\n        }\n    }\n\n    // update (dt) {}\n}\n"]}