{"version": 3, "sources": ["assets/scripts/game/BtnController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,qDAAoD;AACpD,yCAAwC;AACxC,mEAAkE;AAE5D,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAG1C;IAA2C,iCAAY;IAAvD;QAAA,qEAuuBC;QApuBG,gBAAU,GAAY,IAAI,CAAC,CAAC,aAAa;QAGzC,kBAAY,GAAY,IAAI,CAAC,CAAC,SAAS;QAGvC,cAAQ,GAAY,IAAI,CAAC,CAAC,gBAAgB;QAG1C,iBAAW,GAAY,IAAI,CAAC,CAAC,OAAO;QAGpC,cAAQ,GAAY,IAAI,CAAC,CAAC,SAAS;QAGnC,cAAQ,GAAY,IAAI,CAAC,CAAC,SAAS;QAGnC,oBAAc,GAAY,IAAI,CAAC,CAAC,kBAAkB;QAElD,YAAY;QACZ,WAAK,GAAY,IAAI,CAAC;QACtB,WAAK,GAAY,IAAI,CAAC;QAEtB,WAAW;QACX,uBAAiB,GAAY,KAAK,CAAC;QAEnC,wBAAwB;QACxB,uBAAiB,GAAW,GAAG,CAAC,CAAC,iBAAiB;QAElD,YAAY;QACZ,wBAAkB,GAAW,CAAC,CAAC;QAE/B,YAAY;QACZ,iBAAW,GAAY,KAAK,CAAC;QAE7B,eAAe;QACP,wBAAkB,GAAY,KAAK,CAAC;QAE5C,0BAA0B;QAClB,eAAS,GAAY,IAAI,CAAC;QAsZlC,kBAAkB;QACV,oBAAc,GAAY,KAAK,CAAC;;QAoSxC,iBAAiB;IACrB,CAAC;IA1rBG,wBAAwB;IAExB,8BAAM,GAAN;QACI,mBAAmB;QACnB,IAAI,CAAC,KAAK,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;QAChE,IAAI,CAAC,KAAK,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;QAEhE,eAAe;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,UAAU;QACV,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED,6BAAK,GAAL;QAAA,iBA2CC;QAxCG,sBAAsB;QACtB,IAAI,CAAC,sBAAsB,CAAC;YAGxB,sBAAsB;YACtB,KAAI,CAAC,YAAY,CAAC;gBAEd,gCAAgC;gBAChC,KAAI,CAAC,gBAAgB,CAAC,KAAI,CAAC,UAAU,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;oBAEpF,KAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,0BAA0B;gBAEpC,+BAA+B;gBAC/B,KAAI,CAAC,gBAAgB,CAAC,KAAI,CAAC,WAAW,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;oBAErF,KAAI,CAAC,cAAc,EAAE,CAAC;gBAC1B,CAAC,CAAC,CAAC;gBAEH,6BAA6B;gBAC7B,KAAI,CAAC,gBAAgB,EAAE,CAAC;gBAExB,6BAA6B;gBAC7B,KAAI,CAAC,gBAAgB,EAAE,CAAC;gBAExB,uBAAuB;gBACvB,KAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,KAAI,CAAC,mBAAmB,EAAE,CAAC;gBAE3B,oBAAoB;gBACpB,KAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,KAAI,CAAC,kBAAkB,EAAE,CAAC;YAE9B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU;QACvB,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uBAAuB,EAAE,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,cAAc;IACN,0CAAkB,GAA1B;QACI,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAElF,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,KAAK;YACvB,IAAI,GAAG,EAAE;gBACL,gCAAgC;gBAChC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,GAAG,KAAK,CAAC;aAEjD;QACL,CAAC,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;SAEvD;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;SAEnD;IAGL,CAAC;IAED,cAAc;IACN,yCAAiB,GAAzB;QAGI,IAAM,OAAO,GAAG;YACZ,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAC;YACrC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAC;YACtC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAC;YACnC,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAC;SACtC,CAAC;QAEF,OAAO,CAAC,OAAO,CAAC,UAAA,GAAG;YACf,IAAI,GAAG,CAAC,IAAI,EAAE;aAEb;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,oBAAkB,GAAG,CAAC,IAAI,mCAAO,CAAC,CAAC;aACpD;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAID;;OAEG;IACH,wCAAgB,GAAhB,UAAiB,IAAa,EAAE,SAAiB,EAAE,UAAkB,EAAE,aAAuB,EAAE,SAAyB;QAAzH,iBAqCC;QArC+F,0BAAA,EAAA,gBAAyB;QACrH,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,SAAS,CAAC,CAAC;YACnE,OAAO;SACV;QAID,eAAe;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,yBAAyB;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;QAGtC,sBAAsB;QACtB,IAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACrD,IAAI,eAAe,EAAE;YAEjB,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;SACnC;QAED,kBAAkB;QACjB,IAAY,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,cAAc;QAEnD,oCAAoC;QACpC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAEtC,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC;YAEd,KAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;YAEjF,aAAa;YACb,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;QAE1C,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,WAAW;IACH,4CAAoB,GAA5B,UAA6B,IAAa,EAAE,SAAiB,EAAE,UAAkB,EAAE,aAAuB,EAAE,SAAkB;QAA9H,iBAoCC;QAnCG,oBAAoB;QACpB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAEzC,SAAS;QACT,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAEnC,kBAAkB;YAClB,KAAI,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC3C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAEjC,wBAAwB;YAExB,KAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAEtC,mBAAmB;YACnB,IAAI,SAAS,EAAE;gBACX,KAAI,CAAC,oBAAoB,EAAE,CAAC;aAC/B;YAED,IAAI,aAAa,EAAE;gBAEf,aAAa,EAAE,CAAC;aACnB;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YACpC,mBAAmB;YACnB,KAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;IAGb,CAAC;IAED;;OAEG;IACH,uCAAe,GAAf,UAAgB,IAAa,EAAE,SAAiB;QAC5C,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;YACrB,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAC,CAAC,CAAC;YAC7F,OAAO;SACV;QAED,4BAA4B;QAC5B,IAAM,SAAS,GAAG,eAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAG/C,0BAA0B;QAC1B,IAAM,iBAAiB,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,CAAmB,CAAC;QACxF,IAAI,iBAAiB,EAAE;YACnB,gBAAgB;YAChB,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC5C,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,WAAW,GAAG,iBAAiB,CAAC;gBACvC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC5B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;gBAEnB,OAAO,CAAC,YAAY;aACvB;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxD,OAAO;aACV;SACJ;QAED,4BAA4B;QAC5B,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC;QAC3D,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,KAAY,EAAE,WAA2B;YACnF,IAAI,CAAC,KAAK,IAAI,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC/C,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC5C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;oBACjC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;oBAC5B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;iBAEtB;qBAAM;oBACH,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC3D;aACJ;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE;oBACvC,SAAS,EAAE,SAAS;oBACpB,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,OAAO;oBAC/B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;iBACxC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,wCAAgB,GAAhB,UAAiB,IAAa;QAC1B,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,aAAa;QACb,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;IACvB,CAAC;IAID;;OAEG;IACH,wCAAgB,GAAhB;QAAA,iBAqCC;QApCG,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,WAAW;QACX,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC5C,iBAAiB;YACjB,IAAM,UAAU,GAAG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,6BAA6B,CAAC;YAC7F,KAAI,CAAC,eAAe,CAAC,KAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC1C,6BAA6B;YAC7B,KAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,wCAAwC;YACxC,KAAI,CAAC,KAAK,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC;YACzB,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC;YAE7D,wBAAwB;YACxB,IAAM,UAAU,GAAG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,4BAA4B,CAAC;YAC3F,KAAI,CAAC,eAAe,CAAC,KAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEhD,qCAAqC;YACrC,IAAI,KAAI,CAAC,KAAK,EAAE;gBACZ,2BAAY,CAAC,OAAO,EAAE,CAAC;aAC1B;iBAAM;gBACH,2BAAY,CAAC,OAAO,EAAE,CAAC;aAC1B;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YAC7C,gBAAgB;YAChB,KAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACH,wCAAgB,GAAhB;QAAA,iBA8BC;QA7BG,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,WAAW;QACX,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC5C,iBAAiB;YACjB,IAAM,UAAU,GAAG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,6BAA6B,CAAC;YAC7F,KAAI,CAAC,eAAe,CAAC,KAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC1C,6BAA6B;YAC7B,KAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,SAAS;YACT,KAAI,CAAC,KAAK,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC;YACzB,yCAAmB,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC;YAE7D,wBAAwB;YACxB,IAAM,UAAU,GAAG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,4BAA4B,CAAC;YAC3F,KAAI,CAAC,eAAe,CAAC,KAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YAC7C,gBAAgB;YAChB,KAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACH,2CAAmB,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,4BAA4B,CAAC;QACxF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,2CAAmB,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,4BAA4B,CAAC;QACxF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,4CAAoB,GAApB;QACI,uBAAuB;QACvB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,wBAAwB;YACxB,2BAAY,CAAC,eAAe,EAAE,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACH,+CAAuB,GAAvB;QACI,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,oBAAoB;YACpB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,2BAAY,CAAC,OAAO,EAAE,CAAC;aAC1B;SACJ;IACL,CAAC;IAID;;OAEG;IACH,sCAAc,GAAd;QACI,gBAAgB;QAChB,IAAI,IAAI,CAAC,WAAW,EAAE;YAElB,OAAO;SACV;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YACtF,IAAI,oBAAoB,EAAE;gBACtB,oBAAoB;gBACpB,oBAAoB,CAAC,IAAI,CAAC,cAAO,CAAC,CAAC,CAAC;aACvC;SACJ;IACL,CAAC;IAKD;;OAEG;IACH,8CAAsB,GAAtB,UAAuB,QAAkB;QAGrC,iBAAiB;QACjB,IAAM,YAAY,GAAG;YACjB,sBAAsB;YACtB,uBAAuB;YACvB,sBAAsB;YACtB,uBAAuB;YACvB,2BAA2B;YAC3B,4BAA4B;YAC5B,4BAA4B;YAC5B,6BAA6B;YAC7B,2BAA2B;YAC3B,4BAA4B;YAC5B,4BAA4B;YAC5B,6BAA6B;SAChC,CAAC;QAEF,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;QAGvC,mBAAmB;QACnB,IAAI,UAAU,KAAK,CAAC,EAAE;YAClB,QAAQ,EAAE,CAAC;YACX,OAAO;SACV;QAED,UAAU;QACV,YAAY,CAAC,OAAO,CAAC,UAAC,SAAS;YAC3B,IAAM,SAAS,GAAG,eAAM,CAAC,SAAS,GAAG,SAAS,CAAC;YAE/C,cAAc;YACd,IAAM,iBAAiB,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,CAAmB,CAAC;YACxF,IAAI,iBAAiB,EAAE;gBAEnB,WAAW,EAAE,CAAC;gBACd,IAAI,WAAW,IAAI,UAAU,EAAE;oBAE3B,QAAQ,EAAE,CAAC;iBACd;aACJ;iBAAM;gBACH,OAAO;gBAEP,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,KAAY,EAAE,WAA2B;oBACnF,IAAI,CAAC,KAAK,IAAI,WAAW,EAAE;qBAE1B;yBAAM;wBACH,OAAO,CAAC,KAAK,CAAC,uEAA4B,WAAW,GAAG,CAAC,UAAI,UAAU,MAAG,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;qBAC1H;oBAED,WAAW,EAAE,CAAC;oBAEd,IAAI,WAAW,IAAI,UAAU,EAAE;wBAE3B,QAAQ,EAAE,CAAC;qBACd;gBACL,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACH,wCAAgB,GAAhB;QACI,cAAc;QACd,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO;SACV;QAGD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,cAAc;YACd,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC;SAG/B;QAED,SAAS;QACT,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAG/B,CAAC;IAED;;OAEG;IACH,0CAAkB,GAAlB;QAII,eAAe;QACf,IAAI,IAAI,CAAC,WAAW,EAAE;SAErB;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAExB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;aAAM;YAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAGD;;OAEG;IACH,wCAAgB,GAAhB;QAAA,iBA2CC;QA1CG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC9D,OAAO;SACV;QAGD,gBAAgB;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAE9B,iBAAiB;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;QAED,yBAAyB;QACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC;QAG5B,aAAa;QACb,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,wBAAwB;QAExB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;aAClB,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACxB,MAAM,EAAE,GAAG;SACd,EAAE;YACC,MAAM,EAAE,UAAU;SACrB,CAAC;aACD,IAAI,CAAC;YACF,cAAc;YAEd,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAEzB,wBAAwB;YACxB,KAAI,CAAC,eAAe,EAAE,CAAC;QAE3B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,wCAAgB,GAAhB;QAAA,iBAsCC;QArCG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAID,gBAAgB;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAE/B,SAAS;QACT,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,qCAAqC;QACrC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;aAClB,EAAE,CAAC,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;YAC9B,MAAM,EAAE,CAAC;SACZ,EAAE;YACC,MAAM,EAAE,UAAU;SACrB,CAAC;aACD,EAAE,CAAC,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;YAC9B,OAAO,EAAE,CAAC;SACb,EAAE;YACC,MAAM,EAAE,SAAS,CAAC,WAAW;SAChC,CAAC;aACD,IAAI,CAAC;YACF,yBAAyB;YACzB,IAAI,KAAI,CAAC,YAAY,EAAE;gBACnB,KAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;aACpC;YACD,KAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,iBAAiB;YAC9C,KAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAEzB,aAAa;YACb,KAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,qCAAa,GAArB;QACI,gBAAgB;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE1C,uBAAuB;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACtD,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;QACf,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAClB,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QAEjB,uBAAuB;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACtD,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,WAAW;QAE1D,WAAW;QACX,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAErD,oBAAoB;QACpB,IAAM,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,sBAAsB;YACtB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC;SAC/B;QAED,OAAO;QACP,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,uCAAe,GAAvB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACK,wCAAgB,GAAxB;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACK,wCAAgB,GAAxB;QACI,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAE7C,iBAAiB;QACjB,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YAC7C,qBAAqB;YACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;SAC3B;IACL,CAAC;IAED,iCAAS,GAAT;QACI,SAAS;QACT,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YAC1C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACzD,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;IACL,CAAC;IAjuBD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;qDACS;IAG3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;uDACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;mDACO;IAGzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;sDACU;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;mDACO;IAGzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;mDACO;IAGzB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;yDACa;IArBd,aAAa;QADjC,OAAO;OACa,aAAa,CAuuBjC;IAAD,oBAAC;CAvuBD,AAuuBC,CAvuB0C,EAAE,CAAC,SAAS,GAuuBtD;kBAvuBoB,aAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { AudioManager } from \"../util/AudioManager\";\nimport { Config } from \"../util/Config\";\nimport { LocalStorageManager } from \"../util/LocalStorageManager\";\n\nconst {ccclass, property} = cc._decorator;\n\n@ccclass\nexport default class BtnController extends cc.Component {\n\n    @property(cc.Node)\n    settingBtn: cc.Node = null; // 设置按钮（固定不动）\n\n    @property(cc.Node)\n    settingPanel: cc.Node = null; // 设置面板容器\n\n    @property(cc.Node)\n    maskNode: cc.Node = null; // 遮罩节点，用于控制显示区域\n\n    @property(cc.Node)\n    gameplayBtn: cc.Node = null; // 玩法按钮\n\n    @property(cc.Node)\n    musicBtn: cc.Node = null; // 音乐开关按钮\n\n    @property(cc.Node)\n    soundBtn: cc.Node = null; // 音效开关按钮\n\n    @property(cc.Node)\n    infoDialogNode: cc.Node = null; // info_dialog节点引用\n\n    // 音乐和音效开关状态\n    music: boolean = true;\n    sound: boolean = true;\n\n    // 设置面板展开状态\n    isSettingExpanded: boolean = false;\n\n    // 动画时长 - 可以调整这个值来控制动画速度\n    animationDuration: number = 0.5; // 增加到0.5秒，让动画更缓慢\n\n    // 保存遮罩的原始高度\n    originalMaskHeight: number = 0;\n\n    // 防止重复点击的标志\n    isAnimating: boolean = false;\n\n    // 防止重复初始化音乐的标志\n    private isMusicInitialized: boolean = false;\n\n    // 全屏点击遮罩，用于点击屏幕任意位置关闭设置面板\n    private clickMask: cc.Node = null;\n\n    // LIFE-CYCLE CALLBACKS:\n\n    onLoad() {\n        // 从本地存储获取音乐和音效开关状态\n        this.music = LocalStorageManager.GetInstance().getMusicSwitch();\n        this.sound = LocalStorageManager.GetInstance().getSoundSwitch();\n\n        // 初始化设置面板为隐藏状态\n        this.initSettingPanel();\n\n        // 初始化点击遮罩\n        this.initClickMask();\n    }\n\n    start() {\n       \n\n        // 预加载所有按钮资源，然后再设置按钮事件\n        this.preloadButtonResources(() => {\n            \n\n            // 添加一个小延迟，确保所有资源都完全就绪\n            this.scheduleOnce(() => {\n               \n                // 设置主设置按钮点击事件 - 使用新的按压UI样式，播放音效\n                this.setupImageButton(this.settingBtn, 'side_btn_menu_normal', 'side_btn_menu_pressed', () => {\n                   \n                    this.toggleSettingPanel();\n                }, true); // 改为true，播放音效来确认点击是否真的被执行\n\n                // 设置玩法按钮点击事件 - 打开info_dialog页面\n                this.setupImageButton(this.gameplayBtn, 'side_btn_info_normal', 'side_btn_info_pressed', () => {\n                    \n                    this.openInfoDialog();\n                });\n\n                // 设置音乐按钮点击事件 - 使用新的UI样式和开关功能\n                this.setupMusicButton();\n\n                // 设置音效按钮点击事件 - 使用新的UI样式和开关功能\n                this.setupSoundButton();\n\n                // 初始化按钮状态 - 使用新的按钮UI样式\n                this.updateMusicButtonUI();\n                this.updateSoundButtonUI();\n\n                // 立即检查按钮状态和确保按钮在最上层\n                this.checkButtonStates();\n                this.ensureButtonsOnTop();\n                \n            }, 0.2); // 等待200毫秒\n        });\n\n        // 只在首次加载时播放音乐，避免重置正在播放的音乐\n        this.scheduleOnce(() => {\n            this.initializeMusicPlayback();\n        }, 0.1);\n    }\n\n    // 确保所有按钮都在最上层\n    private ensureButtonsOnTop() {\n        const buttons = [this.settingBtn, this.gameplayBtn, this.musicBtn, this.soundBtn];\n\n        buttons.forEach((btn, index) => {\n            if (btn) {\n                // 设置合理的zIndex值，确保按钮在所有棋盘和UI元素之上\n                btn.zIndex = cc.macro.MAX_ZINDEX - 10 + index;\n\n            }\n        });\n\n        // 如果有设置面板，确保它的zIndex也足够高，但低于按钮\n        if (this.settingPanel) {\n            this.settingPanel.zIndex = cc.macro.MAX_ZINDEX - 20;\n\n        }\n\n        if (this.maskNode) {\n            this.maskNode.zIndex = cc.macro.MAX_ZINDEX - 19;\n\n        }\n\n       \n    }\n\n    // 检查按钮状态的调试方法\n    private checkButtonStates() {\n        \n\n        const buttons = [\n            {name: \"设置按钮\", node: this.settingBtn},\n            {name: \"玩法按钮\", node: this.gameplayBtn},\n            {name: \"音乐按钮\", node: this.musicBtn},\n            {name: \"音效按钮\", node: this.soundBtn}\n        ];\n\n        buttons.forEach(btn => {\n            if (btn.node) {\n                \n            } else {\n                console.error(`BtnController: ${btn.name}节点未设置`);\n            }\n        });\n    }\n\n\n\n    /**\n     * 设置图片按钮 - 支持按压状态切换\n     */\n    setupImageButton(node: cc.Node, normalImg: string, pressedImg: string, clickCallback: Function, playSound: boolean = true) {\n        if (!node) {\n            console.error(\"BtnController: setupImageButton - 节点为空\", normalImg);\n            return;\n        }\n\n       \n\n        // 确保节点可以接收触摸事件\n        node.active = true;\n\n        // 将按钮移动到最上层，确保不被其他UI元素遮挡\n        node.zIndex = cc.macro.MAX_ZINDEX - 5;\n        \n\n        // 检查是否有Button组件阻挡触摸事件\n        const buttonComponent = node.getComponent(cc.Button);\n        if (buttonComponent) {\n            \n            buttonComponent.enabled = false;\n        }\n\n        // 强制确保节点具有正确的触摸属性\n        (node as any)._touchListener = null; // 清除可能存在的旧监听器\n\n        // 设置初始状态为normal（使用预加载的资源，应该能立即设置成功）\n        this.setButtonSprite(node, normalImg);\n\n        // 延迟一帧注册触摸事件，确保图片设置完成\n        this.scheduleOnce(() => {\n            \n            this.registerButtonEvents(node, normalImg, pressedImg, clickCallback, playSound);\n\n            // 再次确保按钮在最上层\n            node.zIndex = cc.macro.MAX_ZINDEX - 5;\n            \n        }, 0.1);\n    }\n\n    // 注册按钮触摸事件\n    private registerButtonEvents(node: cc.Node, normalImg: string, pressedImg: string, clickCallback: Function, playSound: boolean) {\n        // 清除之前的事件监听器，避免重复注册\n        node.off(cc.Node.EventType.TOUCH_START);\n        node.off(cc.Node.EventType.TOUCH_END);\n        node.off(cc.Node.EventType.TOUCH_CANCEL);\n\n        // 添加触摸事件\n        node.on(cc.Node.EventType.TOUCH_START, () => {\n            \n            // 按下时切换到pressed状态\n            this.setButtonSprite(node, pressedImg);\n        }, this);\n\n        node.on(cc.Node.EventType.TOUCH_END, () => {\n            \n            // 抬起时切换回normal状态并执行点击回调\n\n            this.setButtonSprite(node, normalImg);\n\n            // 根据参数决定是否播放按钮点击音效\n            if (playSound) {\n                this.playButtonClickSound();\n            }\n\n            if (clickCallback) {\n                \n                clickCallback();\n            }\n        }, this);\n\n        node.on(cc.Node.EventType.TOUCH_CANCEL, () => {\n            // 取消时也要切换回normal状态\n            this.setButtonSprite(node, normalImg);\n        }, this);\n\n        \n    }\n\n    /**\n     * 设置按钮精灵图片 - 优先使用预加载的缓存资源\n     */\n    setButtonSprite(node: cc.Node, imageName: string) {\n        if (!node || !imageName) {\n            console.error(\"BtnController: setButtonSprite - 参数无效\", {node: !!node, imageName: imageName});\n            return;\n        }\n\n        // 使用Config中定义的按钮资源路径，不包含扩展名\n        const imagePath = Config.buttonRes + imageName;\n       \n\n        // 优先从缓存获取（预加载的资源应该已经在缓存中）\n        const cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame) as cc.SpriteFrame;\n        if (cachedSpriteFrame) {\n            // 图片已经在缓存中，直接使用\n            const sprite = node.getComponent(cc.Sprite);\n            if (sprite) {\n                sprite.spriteFrame = cachedSpriteFrame;\n                node.color = cc.Color.WHITE;\n                node.opacity = 255;\n\n                return; // 成功设置，直接返回\n            } else {\n                console.error(\"BtnController: 节点没有Sprite组件\", node.name);\n                return;\n            }\n        }\n\n        // 如果缓存中没有，说明预加载可能失败了，进行备用加载\n        console.warn(\"BtnController: 缓存中没有找到图片，进行备用加载\", imagePath);\n        cc.resources.load(imagePath, cc.SpriteFrame, (error: Error, spriteFrame: cc.SpriteFrame) => {\n            if (!error && spriteFrame && node && node.isValid) {\n                const sprite = node.getComponent(cc.Sprite);\n                if (sprite) {\n                    sprite.spriteFrame = spriteFrame;\n                    node.color = cc.Color.WHITE;\n                    node.opacity = 255;\n                    \n                } else {\n                    console.error(\"BtnController: 节点缺少Sprite组件\", node.name);\n                }\n            } else {\n                console.error(\"BtnController: 备用加载按钮图片失败\", {\n                    imagePath: imagePath,\n                    nodeValid: node && node.isValid,\n                    error: error ? error.message : \"未知错误\"\n                });\n            }\n        });\n    }\n\n    /**\n     * 清理按钮状态 - 移除可能导致高亮效果的设置\n     */\n    cleanButtonState(node: cc.Node) {\n        if (!node) return;\n\n        // 重置节点颜色和透明度\n        node.color = cc.Color.WHITE;\n        node.opacity = 255;\n    }\n\n\n\n    /**\n     * 设置音乐按钮 - 支持开关状态和按压效果\n     */\n    setupMusicButton() {\n        if (!this.musicBtn) return;\n\n        // 更新按钮显示状态\n        this.updateMusicButtonUI();\n\n        // 添加触摸事件\n        this.musicBtn.on(cc.Node.EventType.TOUCH_START, () => {\n            // 按下时显示pressed状态\n            const currentImg = this.music ? 'side_btn_music(on)_pressed' : 'side_btn_music(off)_pressed';\n            this.setButtonSprite(this.musicBtn, currentImg);\n        }, this);\n\n        this.musicBtn.on(cc.Node.EventType.TOUCH_END, () => {\n            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）\n            this.playButtonClickSound();\n\n            // 切换音乐状态 - 模仿SettingDialogController的实现\n            this.music = !this.music;\n            LocalStorageManager.GetInstance().setMusicSwitch(this.music);\n\n            // 立即更新按钮UI到新状态的normal图片\n            const newImgName = this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';\n            this.setButtonSprite(this.musicBtn, newImgName);\n\n            // 直接控制音乐播放，模仿SettingDialogController\n            if (this.music) {\n                AudioManager.playBgm();\n            } else {\n                AudioManager.stopBgm();\n            }\n        }, this);\n\n        this.musicBtn.on(cc.Node.EventType.TOUCH_CANCEL, () => {\n            // 取消时恢复normal状态\n            this.updateMusicButtonUI();\n        }, this);\n    }\n\n    /**\n     * 设置音效按钮 - 支持开关状态和按压效果\n     */\n    setupSoundButton() {\n        if (!this.soundBtn) return;\n\n        // 更新按钮显示状态\n        this.updateSoundButtonUI();\n\n        // 添加触摸事件\n        this.soundBtn.on(cc.Node.EventType.TOUCH_START, () => {\n            // 按下时显示pressed状态\n            const currentImg = this.sound ? 'side_btn_sound(on)_pressed' : 'side_btn_sound(off)_pressed';\n            this.setButtonSprite(this.soundBtn, currentImg);\n        }, this);\n\n        this.soundBtn.on(cc.Node.EventType.TOUCH_END, () => {\n            // 播放按钮点击音效（音乐和音效按钮需要独立的音效播放）\n            this.playButtonClickSound();\n\n            // 切换音效状态\n            this.sound = !this.sound;\n            LocalStorageManager.GetInstance().setSoundSwitch(this.sound);\n\n            // 立即更新按钮UI到新状态的normal图片\n            const newImgName = this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';\n            this.setButtonSprite(this.soundBtn, newImgName);\n        }, this);\n\n        this.soundBtn.on(cc.Node.EventType.TOUCH_CANCEL, () => {\n            // 取消时恢复normal状态\n            this.updateSoundButtonUI();\n        }, this);\n    }\n\n    /**\n     * 更新音乐按钮UI - 根据开关状态显示对应图片\n     */\n    updateMusicButtonUI() {\n        if (!this.musicBtn) return;\n\n        const imgName = this.music ? 'side_btn_music(on)_normal' : 'side_btn_music(off)_normal';\n        this.setButtonSprite(this.musicBtn, imgName);\n    }\n\n    /**\n     * 更新音效按钮UI - 根据开关状态显示对应图片\n     */\n    updateSoundButtonUI() {\n        if (!this.soundBtn) return;\n\n        const imgName = this.sound ? 'side_btn_sound(on)_normal' : 'side_btn_sound(off)_normal';\n        this.setButtonSprite(this.soundBtn, imgName);\n    }\n\n    /**\n     * 播放按钮点击音效 - 模仿项目中的音效播放方式\n     */\n    playButtonClickSound() {\n        // 检查音效开关状态，只有在音效开启时才播放\n        if (this.sound) {\n            // 使用AudioManager的按键音效方法\n            AudioManager.keyingToneAudio();\n        }\n    }\n\n    /**\n     * 初始化音乐播放 - 只在首次加载时播放，避免重置\n     */\n    initializeMusicPlayback() {\n        // 只在未初始化时播放音乐\n        if (!this.isMusicInitialized) {\n            this.isMusicInitialized = true;\n\n            // 检查音乐开关状态，只有开启时才播放\n            if (this.music) {\n                AudioManager.playBgm();\n            }\n        }\n    }\n\n\n\n    /**\n     * 打开info_dialog页面 - 模仿项目中的实现方式\n     */\n    openInfoDialog() {\n        // 如果动画正在播放，禁止操作\n        if (this.isAnimating) {\n           \n            return;\n        }\n\n        // 自动隐藏maskNode（收起设置面板）\n        if (this.isSettingExpanded) {\n            this.hideSettingPanel();\n        }\n\n        // 模仿HallPageController中的实现方式\n        if (this.infoDialogNode) {\n            const infoDialogController = this.infoDialogNode.getComponent(\"InfoDialogController\");\n            if (infoDialogController) {\n                // 调用show方法，传入空的回调函数\n                infoDialogController.show(() => {});\n            }\n        }\n    }\n\n    // 添加初始化标志，防止重复初始化\n    private hasInitialized: boolean = false;\n\n    /**\n     * 预加载所有按钮资源 - 确保按钮图片在设置事件前就已加载完成\n     */\n    preloadButtonResources(callback: Function) {\n        \n\n        // 定义所有需要预加载的按钮资源\n        const buttonImages = [\n            'side_btn_menu_normal',\n            'side_btn_menu_pressed',\n            'side_btn_info_normal',\n            'side_btn_info_pressed',\n            'side_btn_music(on)_normal',\n            'side_btn_music(on)_pressed',\n            'side_btn_music(off)_normal',\n            'side_btn_music(off)_pressed',\n            'side_btn_sound(on)_normal',\n            'side_btn_sound(on)_pressed',\n            'side_btn_sound(off)_normal',\n            'side_btn_sound(off)_pressed'\n        ];\n\n        let loadedCount = 0;\n        const totalCount = buttonImages.length;\n        \n\n        // 如果没有需要加载的资源，直接回调\n        if (totalCount === 0) {\n            callback();\n            return;\n        }\n\n        // 预加载每个资源\n        buttonImages.forEach((imageName) => {\n            const imagePath = Config.buttonRes + imageName;\n\n            // 先检查是否已经在缓存中\n            const cachedSpriteFrame = cc.resources.get(imagePath, cc.SpriteFrame) as cc.SpriteFrame;\n            if (cachedSpriteFrame) {\n               \n                loadedCount++;\n                if (loadedCount >= totalCount) {\n                   \n                    callback();\n                }\n            } else {\n                // 加载资源\n               \n                cc.resources.load(imagePath, cc.SpriteFrame, (error: Error, spriteFrame: cc.SpriteFrame) => {\n                    if (!error && spriteFrame) {\n                        \n                    } else {\n                        console.error(`BtnController: 按钮资源加载失败 [${loadedCount + 1}/${totalCount}]`, imageName, error ? error.message : \"未知错误\");\n                    }\n\n                    loadedCount++;\n                    \n                    if (loadedCount >= totalCount) {\n                       \n                        callback();\n                    }\n                });\n            }\n        });\n    }\n\n    /**\n     * 初始化设置面板状态\n     */\n    initSettingPanel() {\n        // 如果已经初始化过，跳过\n        if (this.hasInitialized) {\n            return;\n        }\n\n        \n        if (this.settingPanel) {\n            // 设置面板初始为隐藏状态\n            this.settingPanel.active = false;\n            \n        }\n\n        // 重要：设置mask节点的初始状态为隐藏（高度为0）\n        if (this.maskNode) {\n            this.maskNode.height = 0;\n            this.maskNode.opacity = 255;\n            \n            \n        }\n\n        // 设置初始状态\n        this.isSettingExpanded = false;\n        this.isAnimating = false;\n        this.hasInitialized = true;\n\n        \n    }\n\n    /**\n     * 切换设置面板的展开/收起状态\n     */\n    toggleSettingPanel() {\n        \n\n\n        // 防止动画进行中的重复点击\n        if (this.isAnimating) {\n            \n        }\n\n        if (this.isSettingExpanded) {\n           \n            this.hideSettingPanel();\n        } else {\n            \n            this.showSettingPanel();\n        }\n    }\n\n\n    /**\n     * 展开设置面板 - mask的size.y从0缓慢增加到275\n     */\n    showSettingPanel() {\n        if (!this.maskNode) {\n            console.error(\"BtnController: showSettingPanel - maskNode为空\");\n            return;\n        }\n\n       \n        // 立即更新状态，防止重复点击\n        this.isAnimating = true;\n        this.isSettingExpanded = true;\n\n        // 显示settingPanel\n        if (this.settingPanel) {\n            this.settingPanel.active = true;\n          \n        }\n\n        // 设置mask初始状态：高度为0，透明度为正常\n        this.maskNode.height = 0;\n        this.maskNode.opacity = 255;\n        \n\n        // 确保按钮始终在最上层\n        this.ensureButtonsOnTop();\n\n        // 执行展开动画 - mask高度从0到275\n       \n        cc.tween(this.maskNode)\n            .to(this.animationDuration, {\n                height: 275\n            }, {\n                easing: 'quartOut'\n            })\n            .call(() => {\n                // 动画完成，解除动画锁定\n\n                this.isAnimating = false;\n\n                // 启用点击遮罩，允许点击屏幕任意位置关闭面板\n                this.enableClickMask();\n\n            })\n            .start();\n    }\n\n    /**\n     * 收起设置面板 - mask的size.y从275缓慢减少到0\n     */\n    hideSettingPanel() {\n        if (!this.maskNode) {\n            return;\n        }\n\n       \n\n        // 立即更新状态，防止重复点击\n        this.isAnimating = true;\n        this.isSettingExpanded = false;\n\n        // 禁用点击遮罩\n        this.disableClickMask();\n\n        // 执行收起动画 - mask高度从275缓慢减少到0，同时添加渐隐效果\n        cc.tween(this.maskNode)\n            .to(this.animationDuration * 0.7, {\n                height: 0\n            }, {\n                easing: 'quartOut'\n            })\n            .to(this.animationDuration * 0.3, {\n                opacity: 0\n            }, {\n                easing: 'quartIn' // 最后阶段快速渐隐\n            })\n            .call(() => {\n                // 动画完成后隐藏面板，解除动画锁定，恢复透明度\n                if (this.settingPanel) {\n                    this.settingPanel.active = false;\n                }\n                this.maskNode.opacity = 255; // 恢复透明度，为下次展开做准备\n                this.isAnimating = false;\n\n                // 确保按钮始终在最上层\n                this.ensureButtonsOnTop();\n            })\n            .start();\n    }\n\n    /**\n     * 初始化点击遮罩\n     */\n    private initClickMask() {\n        // 创建一个全屏的透明遮罩节点\n        this.clickMask = new cc.Node(\"ClickMask\");\n\n        // 添加Widget组件，让遮罩铺满整个屏幕\n        const widget = this.clickMask.addComponent(cc.Widget);\n        widget.isAlignTop = true;\n        widget.isAlignBottom = true;\n        widget.isAlignLeft = true;\n        widget.isAlignRight = true;\n        widget.top = 0;\n        widget.bottom = 0;\n        widget.left = 0;\n        widget.right = 0;\n\n        // 添加Button组件，确保能接收点击事件\n        const button = this.clickMask.addComponent(cc.Button);\n        button.transition = cc.Button.Transition.NONE; // 不要视觉过渡效果\n\n        // 添加点击事件监听\n        button.node.on('click', this.onClickMaskClick, this);\n\n        // 添加到Canvas，但设置为不可见\n        const canvas = cc.find('Canvas');\n        if (canvas) {\n            canvas.addChild(this.clickMask);\n            // 设置为较低层级，但要高于棋盘等游戏元素\n            this.clickMask.zIndex = 100;\n        }\n\n        // 默认禁用\n        this.clickMask.active = false;\n    }\n\n    /**\n     * 启用点击遮罩\n     */\n    private enableClickMask() {\n        if (this.clickMask) {\n            this.clickMask.active = true;\n            console.log(\"BtnController: 点击遮罩已启用\");\n        }\n    }\n\n    /**\n     * 禁用点击遮罩\n     */\n    private disableClickMask() {\n        if (this.clickMask) {\n            this.clickMask.active = false;\n            console.log(\"BtnController: 点击遮罩已禁用\");\n        }\n    }\n\n    /**\n     * 点击遮罩的处理函数\n     */\n    private onClickMaskClick() {\n        console.log(\"BtnController: 检测到点击遮罩，关闭设置面板\");\n\n        // 检查面板是否展开且不在动画中\n        if (this.isSettingExpanded && !this.isAnimating) {\n            // 关闭设置面板，相当于再次点击设置按钮\n            this.hideSettingPanel();\n        }\n    }\n\n    onDestroy() {\n        // 清理点击遮罩\n        if (this.clickMask && this.clickMask.isValid) {\n            this.clickMask.off('click', this.onClickMaskClick, this);\n            this.clickMask.removeFromParent();\n            this.clickMask = null;\n        }\n    }\n\n    // update (dt) {}\n}\n"]}