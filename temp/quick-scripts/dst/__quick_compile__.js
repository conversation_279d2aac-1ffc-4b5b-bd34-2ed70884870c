
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":23,"./assets/meshTools/tools/MeshSdkApi":78,"./assets/meshTools/tools/Publish":20,"./assets/meshTools/tools/MeshSdk":3,"./assets/scripts/TipsDialogController":15,"./assets/scripts/ToastController":24,"./assets/scripts/GlobalManagerController":16,"./assets/scripts/bean/GameBean":4,"./assets/scripts/bean/GlobalBean":18,"./assets/scripts/bean/LanguageType":17,"./assets/scripts/bean/EnumBean":38,"./assets/scripts/common/GameData":9,"./assets/scripts/common/GameMgr":21,"./assets/scripts/common/GameTools":19,"./assets/scripts/common/MineConsole":62,"./assets/scripts/common/EventCenter":25,"./assets/scripts/game/ChessBoardController1":22,"./assets/scripts/game/CongratsDialogController":29,"./assets/scripts/game/GamePageController":26,"./assets/scripts/game/GameScoreController":32,"./assets/scripts/game/BtnController":54,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/ChessBoardController":33,"./assets/scripts/hall/HallCenterLayController":27,"./assets/scripts/hall/HallCreateRoomController":28,"./assets/scripts/hall/HallJoinRoomController":30,"./assets/scripts/hall/HallPageController":36,"./assets/scripts/hall/HallParentController":31,"./assets/scripts/hall/InfoDialogController":34,"./assets/scripts/hall/KickOutDialogController":35,"./assets/scripts/hall/LeaveDialogController":37,"./assets/scripts/hall/LevelSelectDemo":61,"./assets/scripts/hall/MatchParentController":41,"./assets/scripts/hall/PlayerLayoutController":39,"./assets/scripts/hall/SettingDialogController":40,"./assets/scripts/hall/TopUpDialogController":43,"./assets/scripts/hall/HallAutoController":53,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectDemoInLevel":50,"./assets/scripts/hall/Level/LevelSelectExample":45,"./assets/scripts/hall/Level/LevelSelectPageController":42,"./assets/scripts/hall/Level/LevelSelectTest":44,"./assets/scripts/hall/Level/ScrollViewHelper":46,"./assets/scripts/hall/Level/LevelItemController":48,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":47,"./assets/scripts/net/HttpUtils":51,"./assets/scripts/net/IHttpMsgBody":49,"./assets/scripts/net/MessageBaseBean":52,"./assets/scripts/net/MessageId":75,"./assets/scripts/net/WebSocketManager":55,"./assets/scripts/net/WebSocketTool":57,"./assets/scripts/net/ErrorCode":56,"./assets/scripts/pfb/InfoItemController":11,"./assets/scripts/pfb/InfoItemOneController":74,"./assets/scripts/pfb/MatchItemController":58,"./assets/scripts/pfb/PlayerGameController ":65,"./assets/scripts/pfb/PlayerScoreController":60,"./assets/scripts/pfb/SeatItemController":59,"./assets/scripts/pfb/CongratsItemController":66,"./assets/scripts/start_up/StartUpPageController":68,"./assets/scripts/start_up/StartUpCenterController":12,"./assets/scripts/test/NoticeRoundStartTest":14,"./assets/scripts/util/AudioMgr":13,"./assets/scripts/util/BlockingQueue":63,"./assets/scripts/util/Config":64,"./assets/scripts/util/Dictionary":76,"./assets/scripts/util/LocalStorageManager":67,"./assets/scripts/util/NickNameLabel":69,"./assets/scripts/util/Tools":70,"./assets/scripts/util/AudioManager":71,"./assets/meshTools/MeshTools":72,"./assets/resources/i18n/zh_HK":77,"./assets/resources/i18n/en":7,"./assets/resources/i18n/zh_CN":73},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":46},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":72,"../../meshTools/Singleton":8,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/EventCenter":25,"../common/GameMgr":21,"../net/MessageBaseBean":52,"../util/Config":64},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"./Config":64,"./Dictionary":76},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"../common/GameMgr":21,"../common/EventCenter":25,"../net/MessageId":75},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./util/Config":64,"./util/Tools":70},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"./TipsDialogController":15,"./ToastController":24,"../meshTools/MeshTools":72,"../meshTools/tools/Publish":20,"./bean/GlobalBean":18,"./bean/LanguageType":17,"./bean/EnumBean":38,"./common/GameMgr":21,"./common/EventCenter":25,"./game/GamePageController":26,"./hall/TopUpDialogController":43,"./hall/HallPageController":36,"./net/GameServerUrl":10,"./net/MessageBaseBean":52,"./net/MessageId":75,"./net/WebSocketManager":55,"./net/WebSocketTool":57,"./net/ErrorCode":56,"./start_up/StartUpPageController":68,"./util/Config":64,"./util/AudioMgr":13},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":53},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":78,"./EventCenter":25,"./GameData":9,"./GameTools":19,"./MineConsole":62},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/ChessBoardController1.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":21},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"./CongratsDialogController":29,"./GameScoreController":32,"../bean/GlobalBean":18,"../hall/LeaveDialogController":37,"../util/Config":64,"../util/Tools":70,"../util/AudioManager":71,"./Chess/ChessBoardController":33,"../net/MessageId":75,"../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../bean/GlobalBean":18,"../net/MessageId":75,"../net/WebSocketManager":55,"../ToastController":24,"./HallAutoController":53,"./HallCreateRoomController":28,"./HallJoinRoomController":30},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../bean/GlobalBean":18,"../pfb/SeatItemController":59,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../bean/GlobalBean":18,"../common/GameMgr":21,"../common/EventCenter":25,"../net/MessageBaseBean":52,"../pfb/CongratsItemController":66,"../util/Tools":70,"../util/Config":64},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../../meshTools/tools/Publish":20,"../bean/GlobalBean":18,"../common/GameMgr":21,"../net/MessageId":75,"../net/WebSocketManager":55,"../ToastController":24,"../util/Config":64,"../util/Tools":70,"./HallCenterLayController":27},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../bean/GlobalBean":18,"../pfb/PlayerScoreController":60},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../../bean/GlobalBean":18,"../../pfb/PlayerGameController ":65},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../pfb/InfoItemController":11,"../pfb/InfoItemOneController":74,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../net/MessageId":75,"../net/WebSocketManager":55,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../bean/GlobalBean":18,"../common/GameMgr":21,"../net/MessageId":75,"../net/WebSocketManager":55,"../net/WebSocketTool":57,"../ToastController":24,"../util/AudioManager":71,"./HallParentController":31,"./InfoDialogController":34,"./KickOutDialogController":35,"./LeaveDialogController":37,"./MatchParentController":41,"./SettingDialogController":40},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../common/GameMgr":21,"../net/MessageId":75,"../net/WebSocketManager":55,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../bean/GlobalBean":18,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":20,"../util/AudioManager":71,"../util/Config":64,"../util/LocalStorageManager":67,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../../meshTools/tools/Publish":20,"../bean/GlobalBean":18,"../common/EventCenter":25,"../common/GameMgr":21,"../net/MessageBaseBean":52,"../pfb/MatchItemController":58,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"../common/GameMgr":21,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"./LevelSelectController":6,"./ScrollViewHelper":46},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectTest.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./HttpUtils":51,"./MessageBaseBean":52,"./GameServerUrl":10,"../../meshTools/MeshTools":72,"../common/GameMgr":21,"../common/EventCenter":25},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectDemoInLevel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{"../bean/GlobalBean":18,"../util/Config":64,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../util/AudioManager":71,"../util/Config":64,"../util/LocalStorageManager":67},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":25,"../common/GameMgr":21,"./WebSocketTool":57},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"./MessageBaseBean":52,"./MessageId":75,"../util/Tools":70,"../../meshTools/Singleton":8,"../common/EventCenter":25,"../common/GameMgr":21},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../bean/GlobalBean":18,"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":70,"../util/Config":64,"../../meshTools/tools/Publish":20},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{"../common/GameMgr":21,"./StartUpCenterController":12},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./AudioManager":71,"./Config":64},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./AudioMgr":13,"./LocalStorageManager":67},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"./tools/Publish":20},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{"../MeshTools":72,"../BaseSDK":23,"../../scripts/net/MessageBaseBean":52,"../../scripts/common/GameMgr":21,"../../scripts/common/EventCenter":25,"MeshSdk":3},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    