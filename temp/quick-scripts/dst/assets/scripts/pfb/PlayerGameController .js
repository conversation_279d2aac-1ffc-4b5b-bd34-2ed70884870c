
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/pfb/PlayerGameController .js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd06f40RGNtELrdV8XT/vrpM', 'PlayerGameController ');
// scripts/pfb/PlayerGameController .ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Tools_1 = require("../util/Tools");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var PlayerGameController = /** @class */ (function (_super) {
    __extends(PlayerGameController, _super);
    function PlayerGameController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.avatar = null; //头像
        _this.flagNode = null; //旗子节点
        _this.addScoreNode = null; //加分背景节点 addscore
        _this.subScoreNode = null; //减分背景节点 deductscore
        return _this;
        // update (dt) {}
    }
    PlayerGameController.prototype.start = function () {
    };
    PlayerGameController.prototype.setData = function (user) {
        var _this = this;
        this.scheduleOnce(function () {
            if (user == null) {
                _this.avatar.active = false;
            }
            else {
                Tools_1.Tools.setNodeSpriteFrameUrl(_this.avatar, user.avatar); //添加头像
                _this.avatar.active = true;
            }
        }, 0.1);
    };
    /**
     * 显示加分效果，带动画
     * @param addValue 加分数值
     */
    PlayerGameController.prototype.showAddScore = function (addValue) {
        var _this = this;
        if (this.addScoreNode) {
            // 获取change_score文本节点并设置加分文本
            var changeScoreLabel = this.addScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "+" + addValue.toString();
                }
            }
            else {
                console.warn("PlayerGame addScoreNode中找不到change_score子节点");
            }
            // 停止之前的动画
            cc.Tween.stopAllByTarget(this.addScoreNode);
            // 重置节点状态
            this.addScoreNode.active = true;
            this.addScoreNode.opacity = 0;
            this.addScoreNode.scale = 0.8;
            // 保存原始位置
            var originalY_1 = this.addScoreNode.y;
            // 使用新的Tween API
            cc.tween(this.addScoreNode)
                .parallel(cc.tween().to(0.15, { opacity: 255 }), cc.tween().to(0.15, { scale: 1.1 }), cc.tween().by(0.15, { y: 15 }))
                .to(0.1, { scale: 1.0 })
                .delay(0.8)
                .parallel(cc.tween().to(0.25, { opacity: 0 }), cc.tween().to(0.25, { scale: 0.9 }), cc.tween().by(0.25, { y: 8 }))
                .call(function () {
                _this.addScoreNode.active = false;
                _this.addScoreNode.opacity = 255;
                _this.addScoreNode.scale = 1.0;
                _this.addScoreNode.y = originalY_1;
            })
                .start();
        }
        else {
            console.warn("PlayerGame addScoreNode未设置");
        }
    };
    /**
     * 显示减分效果
     * @param subValue 减分数值
     */
    PlayerGameController.prototype.showSubScore = function (subValue) {
        var _this = this;
        if (this.subScoreNode) {
            // 获取change_score文本节点并设置减分文本
            var changeScoreLabel = this.subScoreNode.getChildByName("change_score");
            if (changeScoreLabel) {
                var labelComponent = changeScoreLabel.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = "-" + subValue.toString();
                }
            }
            else {
                console.warn("PlayerGame subScoreNode中找不到change_score子节点");
            }
            this.subScoreNode.active = true;
            // 1秒后隐藏
            this.scheduleOnce(function () {
                if (_this.subScoreNode) {
                    _this.subScoreNode.active = false;
                }
            }, 1.0);
        }
        else {
            console.warn("PlayerGame subScoreNode未设置");
        }
    };
    // 隐藏加减分节点
    PlayerGameController.prototype.hideScoreEffects = function () {
        if (this.addScoreNode) {
            this.addScoreNode.active = false;
        }
        if (this.subScoreNode) {
            this.subScoreNode.active = false;
        }
    };
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "avatar", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "flagNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "addScoreNode", void 0);
    __decorate([
        property(cc.Node)
    ], PlayerGameController.prototype, "subScoreNode", void 0);
    PlayerGameController = __decorate([
        ccclass
    ], PlayerGameController);
    return PlayerGameController;
}(cc.Component));
exports.default = PlayerGameController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL3BmYi9QbGF5ZXJHYW1lQ29udHJvbGxlciAudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG9CQUFvQjtBQUNwQiw0RUFBNEU7QUFDNUUsbUJBQW1CO0FBQ25CLHNGQUFzRjtBQUN0Riw4QkFBOEI7QUFDOUIsc0ZBQXNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHdEYsdUNBQXNDO0FBRWhDLElBQUEsS0FBc0IsRUFBRSxDQUFDLFVBQVUsRUFBbEMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFpQixDQUFDO0FBRzFDO0lBQWtELHdDQUFZO0lBQTlEO1FBQUEscUVBdUlDO1FBcElHLFlBQU0sR0FBWSxJQUFJLENBQUMsQ0FBRyxJQUFJO1FBRTlCLGNBQVEsR0FBWSxJQUFJLENBQUMsQ0FBRSxNQUFNO1FBRWpDLGtCQUFZLEdBQVksSUFBSSxDQUFDLENBQUUsaUJBQWlCO1FBRWhELGtCQUFZLEdBQVksSUFBSSxDQUFDLENBQUUsb0JBQW9COztRQTZIbkQsaUJBQWlCO0lBQ3JCLENBQUM7SUExSEcsb0NBQUssR0FBTDtJQUVBLENBQUM7SUFFRCxzQ0FBTyxHQUFQLFVBQVEsSUFBYztRQUF0QixpQkFTQztRQVJHLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxJQUFJLElBQUksSUFBSSxJQUFJLEVBQUU7Z0JBQ2QsS0FBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO2FBQzlCO2lCQUFNO2dCQUNILGFBQUssQ0FBQyxxQkFBcUIsQ0FBQyxLQUFJLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFBLE1BQU07Z0JBQzVELEtBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQzthQUM3QjtRQUNMLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztJQUNaLENBQUM7SUFJRDs7O09BR0c7SUFDSCwyQ0FBWSxHQUFaLFVBQWEsUUFBZ0I7UUFBN0IsaUJBc0RDO1FBbkRHLElBQUksSUFBSSxDQUFDLFlBQVksRUFBRTtZQUNuQiw0QkFBNEI7WUFDNUIsSUFBTSxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLGNBQWMsQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUMxRSxJQUFJLGdCQUFnQixFQUFFO2dCQUNsQixJQUFNLGNBQWMsR0FBRyxnQkFBZ0IsQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUMvRCxJQUFJLGNBQWMsRUFBRTtvQkFDaEIsY0FBYyxDQUFDLE1BQU0sR0FBRyxHQUFHLEdBQUcsUUFBUSxDQUFDLFFBQVEsRUFBRSxDQUFDO2lCQUVyRDthQUNKO2lCQUFNO2dCQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsNENBQTRDLENBQUMsQ0FBQzthQUM5RDtZQUlELFVBQVU7WUFDVixFQUFFLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7WUFFNUMsU0FBUztZQUNULElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztZQUNoQyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUM7WUFDOUIsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLEdBQUcsR0FBRyxDQUFDO1lBRTlCLFNBQVM7WUFDVCxJQUFJLFdBQVMsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQztZQUVwQyxnQkFBZ0I7WUFDaEIsRUFBRSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDO2lCQUN0QixRQUFRLENBQ0wsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxPQUFPLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFDckMsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxLQUFLLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFDbkMsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FDakM7aUJBQ0EsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFLEtBQUssRUFBRSxHQUFHLEVBQUUsQ0FBQztpQkFDdkIsS0FBSyxDQUFDLEdBQUcsQ0FBQztpQkFDVixRQUFRLENBQ0wsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxPQUFPLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFDbkMsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxLQUFLLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFDbkMsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FDaEM7aUJBQ0EsSUFBSSxDQUFDO2dCQUNGLEtBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztnQkFDakMsS0FBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEdBQUcsR0FBRyxDQUFDO2dCQUNoQyxLQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssR0FBRyxHQUFHLENBQUM7Z0JBQzlCLEtBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxHQUFHLFdBQVMsQ0FBQztZQUVwQyxDQUFDLENBQUM7aUJBQ0QsS0FBSyxFQUFFLENBQUM7U0FDaEI7YUFBTTtZQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsNEJBQTRCLENBQUMsQ0FBQztTQUM5QztJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSCwyQ0FBWSxHQUFaLFVBQWEsUUFBZ0I7UUFBN0IsaUJBNEJDO1FBekJHLElBQUksSUFBSSxDQUFDLFlBQVksRUFBRTtZQUNuQiw0QkFBNEI7WUFDNUIsSUFBTSxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLGNBQWMsQ0FBQyxjQUFjLENBQUMsQ0FBQztZQUMxRSxJQUFJLGdCQUFnQixFQUFFO2dCQUNsQixJQUFNLGNBQWMsR0FBRyxnQkFBZ0IsQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDO2dCQUMvRCxJQUFJLGNBQWMsRUFBRTtvQkFDaEIsY0FBYyxDQUFDLE1BQU0sR0FBRyxHQUFHLEdBQUcsUUFBUSxDQUFDLFFBQVEsRUFBRSxDQUFDO2lCQUVyRDthQUNKO2lCQUFNO2dCQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsNENBQTRDLENBQUMsQ0FBQzthQUM5RDtZQUVELElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztZQUVoQyxRQUFRO1lBQ1IsSUFBSSxDQUFDLFlBQVksQ0FBQztnQkFDZCxJQUFJLEtBQUksQ0FBQyxZQUFZLEVBQUU7b0JBQ25CLEtBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztpQkFFcEM7WUFDTCxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7U0FDWDthQUFNO1lBQ0gsT0FBTyxDQUFDLElBQUksQ0FBQyw0QkFBNEIsQ0FBQyxDQUFDO1NBQzlDO0lBQ0wsQ0FBQztJQUVELFVBQVU7SUFDViwrQ0FBZ0IsR0FBaEI7UUFDSSxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1NBQ3BDO1FBQ0QsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ25CLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztTQUNwQztJQUNMLENBQUM7SUFqSUQ7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzt3REFDSztJQUV2QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzBEQUNPO0lBRXpCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7OERBQ1c7SUFFN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDVztJQVRaLG9CQUFvQjtRQUR4QyxPQUFPO09BQ2Esb0JBQW9CLENBdUl4QztJQUFELDJCQUFDO0NBdklELEFBdUlDLENBdklpRCxFQUFFLENBQUMsU0FBUyxHQXVJN0Q7a0JBdklvQixvQkFBb0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMZWFybiBUeXBlU2NyaXB0OlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3R5cGVzY3JpcHQuaHRtbFxuLy8gTGVhcm4gQXR0cmlidXRlOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3JlZmVyZW5jZS9hdHRyaWJ1dGVzLmh0bWxcbi8vIExlYXJuIGxpZmUtY3ljbGUgY2FsbGJhY2tzOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL2xpZmUtY3ljbGUtY2FsbGJhY2tzLmh0bWxcblxuaW1wb3J0IHsgUm9vbVVzZXIgfSBmcm9tIFwiLi4vYmVhbi9HYW1lQmVhblwiO1xuaW1wb3J0IHsgVG9vbHMgfSBmcm9tIFwiLi4vdXRpbC9Ub29sc1wiO1xuXG5jb25zdCB7Y2NjbGFzcywgcHJvcGVydHl9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFBsYXllckdhbWVDb250cm9sbGVyIGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGF2YXRhcjogY2MuTm9kZSA9IG51bGw7ICAgLy/lpLTlg49cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBmbGFnTm9kZTogY2MuTm9kZSA9IG51bGw7ICAvL+aXl+WtkOiKgueCuVxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGFkZFNjb3JlTm9kZTogY2MuTm9kZSA9IG51bGw7ICAvL+WKoOWIhuiDjOaZr+iKgueCuSBhZGRzY29yZVxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHN1YlNjb3JlTm9kZTogY2MuTm9kZSA9IG51bGw7ICAvL+WHj+WIhuiDjOaZr+iKgueCuSBkZWR1Y3RzY29yZVxuXG4gXG5cbiAgICBzdGFydCAoKSB7XG4gICAgXG4gICAgfVxuXG4gICAgc2V0RGF0YSh1c2VyOiBSb29tVXNlcil7XG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIGlmICh1c2VyID09IG51bGwpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmF2YXRhci5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgVG9vbHMuc2V0Tm9kZVNwcml0ZUZyYW1lVXJsKHRoaXMuYXZhdGFyLCB1c2VyLmF2YXRhcik7Ly/mt7vliqDlpLTlg49cbiAgICAgICAgICAgICAgICB0aGlzLmF2YXRhci5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCAwLjEpO1xuICAgIH1cblxuICAgXG5cbiAgICAvKipcbiAgICAgKiDmmL7npLrliqDliIbmlYjmnpzvvIzluKbliqjnlLtcbiAgICAgKiBAcGFyYW0gYWRkVmFsdWUg5Yqg5YiG5pWw5YC8XG4gICAgICovXG4gICAgc2hvd0FkZFNjb3JlKGFkZFZhbHVlOiBudW1iZXIpIHtcbiAgICAgIFxuXG4gICAgICAgIGlmICh0aGlzLmFkZFNjb3JlTm9kZSkge1xuICAgICAgICAgICAgLy8g6I635Y+WY2hhbmdlX3Njb3Jl5paH5pys6IqC54K55bm26K6+572u5Yqg5YiG5paH5pysXG4gICAgICAgICAgICBjb25zdCBjaGFuZ2VTY29yZUxhYmVsID0gdGhpcy5hZGRTY29yZU5vZGUuZ2V0Q2hpbGRCeU5hbWUoXCJjaGFuZ2Vfc2NvcmVcIik7XG4gICAgICAgICAgICBpZiAoY2hhbmdlU2NvcmVMYWJlbCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGxhYmVsQ29tcG9uZW50ID0gY2hhbmdlU2NvcmVMYWJlbC5nZXRDb21wb25lbnQoY2MuTGFiZWwpO1xuICAgICAgICAgICAgICAgIGlmIChsYWJlbENvbXBvbmVudCkge1xuICAgICAgICAgICAgICAgICAgICBsYWJlbENvbXBvbmVudC5zdHJpbmcgPSBcIitcIiArIGFkZFZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiUGxheWVyR2FtZSBhZGRTY29yZU5vZGXkuK3mib7kuI3liLBjaGFuZ2Vfc2NvcmXlrZDoioLngrlcIik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgXG5cbiAgICAgICAgICAgIC8vIOWBnOatouS5i+WJjeeahOWKqOeUu1xuICAgICAgICAgICAgY2MuVHdlZW4uc3RvcEFsbEJ5VGFyZ2V0KHRoaXMuYWRkU2NvcmVOb2RlKTtcblxuICAgICAgICAgICAgLy8g6YeN572u6IqC54K554q25oCBXG4gICAgICAgICAgICB0aGlzLmFkZFNjb3JlTm9kZS5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICAgdGhpcy5hZGRTY29yZU5vZGUub3BhY2l0eSA9IDA7XG4gICAgICAgICAgICB0aGlzLmFkZFNjb3JlTm9kZS5zY2FsZSA9IDAuODtcblxuICAgICAgICAgICAgLy8g5L+d5a2Y5Y6f5aeL5L2N572uXG4gICAgICAgICAgICBsZXQgb3JpZ2luYWxZID0gdGhpcy5hZGRTY29yZU5vZGUueTtcblxuICAgICAgICAgICAgLy8g5L2/55So5paw55qEVHdlZW4gQVBJXG4gICAgICAgICAgICBjYy50d2Vlbih0aGlzLmFkZFNjb3JlTm9kZSlcbiAgICAgICAgICAgICAgICAucGFyYWxsZWwoXG4gICAgICAgICAgICAgICAgICAgIGNjLnR3ZWVuKCkudG8oMC4xNSwgeyBvcGFjaXR5OiAyNTUgfSksXG4gICAgICAgICAgICAgICAgICAgIGNjLnR3ZWVuKCkudG8oMC4xNSwgeyBzY2FsZTogMS4xIH0pLFxuICAgICAgICAgICAgICAgICAgICBjYy50d2VlbigpLmJ5KDAuMTUsIHsgeTogMTUgfSlcbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgLnRvKDAuMSwgeyBzY2FsZTogMS4wIH0pXG4gICAgICAgICAgICAgICAgLmRlbGF5KDAuOClcbiAgICAgICAgICAgICAgICAucGFyYWxsZWwoXG4gICAgICAgICAgICAgICAgICAgIGNjLnR3ZWVuKCkudG8oMC4yNSwgeyBvcGFjaXR5OiAwIH0pLFxuICAgICAgICAgICAgICAgICAgICBjYy50d2VlbigpLnRvKDAuMjUsIHsgc2NhbGU6IDAuOSB9KSxcbiAgICAgICAgICAgICAgICAgICAgY2MudHdlZW4oKS5ieSgwLjI1LCB7IHk6IDggfSlcbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgLmNhbGwoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmFkZFNjb3JlTm9kZS5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRTY29yZU5vZGUub3BhY2l0eSA9IDI1NTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5hZGRTY29yZU5vZGUuc2NhbGUgPSAxLjA7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuYWRkU2NvcmVOb2RlLnkgPSBvcmlnaW5hbFk7XG4gICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgLnN0YXJ0KCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJQbGF5ZXJHYW1lIGFkZFNjb3JlTm9kZeacquiuvue9rlwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaYvuekuuWHj+WIhuaViOaenFxuICAgICAqIEBwYXJhbSBzdWJWYWx1ZSDlh4/liIbmlbDlgLxcbiAgICAgKi9cbiAgICBzaG93U3ViU2NvcmUoc3ViVmFsdWU6IG51bWJlcikge1xuICAgICAgIFxuXG4gICAgICAgIGlmICh0aGlzLnN1YlNjb3JlTm9kZSkge1xuICAgICAgICAgICAgLy8g6I635Y+WY2hhbmdlX3Njb3Jl5paH5pys6IqC54K55bm26K6+572u5YeP5YiG5paH5pysXG4gICAgICAgICAgICBjb25zdCBjaGFuZ2VTY29yZUxhYmVsID0gdGhpcy5zdWJTY29yZU5vZGUuZ2V0Q2hpbGRCeU5hbWUoXCJjaGFuZ2Vfc2NvcmVcIik7XG4gICAgICAgICAgICBpZiAoY2hhbmdlU2NvcmVMYWJlbCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGxhYmVsQ29tcG9uZW50ID0gY2hhbmdlU2NvcmVMYWJlbC5nZXRDb21wb25lbnQoY2MuTGFiZWwpO1xuICAgICAgICAgICAgICAgIGlmIChsYWJlbENvbXBvbmVudCkge1xuICAgICAgICAgICAgICAgICAgICBsYWJlbENvbXBvbmVudC5zdHJpbmcgPSBcIi1cIiArIHN1YlZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiUGxheWVyR2FtZSBzdWJTY29yZU5vZGXkuK3mib7kuI3liLBjaGFuZ2Vfc2NvcmXlrZDoioLngrlcIik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHRoaXMuc3ViU2NvcmVOb2RlLmFjdGl2ZSA9IHRydWU7XG5cbiAgICAgICAgICAgIC8vIDHnp5LlkI7pmpDol49cbiAgICAgICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5zdWJTY29yZU5vZGUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdWJTY29yZU5vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgMS4wKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcIlBsYXllckdhbWUgc3ViU2NvcmVOb2Rl5pyq6K6+572uXCIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g6ZqQ6JeP5Yqg5YeP5YiG6IqC54K5XG4gICAgaGlkZVNjb3JlRWZmZWN0cygpIHtcbiAgICAgICAgaWYgKHRoaXMuYWRkU2NvcmVOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmFkZFNjb3JlTm9kZS5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5zdWJTY29yZU5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMuc3ViU2NvcmVOb2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8gdXBkYXRlIChkdCkge31cbn1cbiJdfQ==