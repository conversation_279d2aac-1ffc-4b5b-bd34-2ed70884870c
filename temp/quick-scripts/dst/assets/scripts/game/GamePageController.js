
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //棋盘控制器
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        console.log("🎮🎮🎮 收到GameStart消息，开始重置游戏场景 🎮🎮🎮");
        console.log("GameStart数据:", data);
        // 重置游戏场景：清除数字、炸弹和标记的预制体，重新显示小格子
        if (this.chessBoardController) {
            console.log("✅ chessBoardController 存在，开始调用 resetGameScene");
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1 && data.validHexCoords) {
            // 六边形地图，暂时使用默认值，后续可根据实际需求调整
            this.currentMineCount = Math.floor(data.validHexCoords.length * 0.15); // 约15%的格子是炸弹
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
        console.log("✅ GameStart处理完成，游戏场景已重置");
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        console.log("🧪 手动测试重置功能");
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        var _this = this;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 在棋盘上显示所有玩家的操作
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
        // 使用setTimeout作为备选方案
        setTimeout(function () {
            _this.updateBoardAfterActions(data);
        }, 1000);
        // 同时也使用scheduleOnce
        this.scheduleOnce(this.delayedUpdateBoard.bind(this, data), 1.0);
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 第一步：先让所有头像消失
        var _this = this;
        this.hideAllAvatars(data.playerActions, function () {
            // 头像消失完成后，执行后续操作
            // 第二步：处理每个玩家的操作结果
            // 先按位置分组，处理同一位置有多个操作的情况
            var processedPositions = new Set();
            data.playerActions.forEach(function (action) {
                var positionKey = action.x + "," + action.y;
                // 如果这个位置已经处理过，跳过
                if (processedPositions.has(positionKey)) {
                    return;
                }
                // 查找同一位置的所有操作
                var samePositionActions = data.playerActions.filter(function (a) {
                    return a.x === action.x && a.y === action.y;
                });
                // 处理同一位置的操作结果
                _this.processPositionResult(action.x, action.y, samePositionActions);
                // 标记这个位置已处理
                processedPositions.add(positionKey);
            });
            // 第三步：处理连锁展开结果
            if (data.floodFillResults && data.floodFillResults.length > 0) {
                data.floodFillResults.forEach(function (floodFill) {
                    _this.processFloodFillResult(floodFill);
                });
            }
        });
    };
    /**
     * 让所有头像消失（简化版：直接删除所有头像）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 直接调用一次头像删除，不区分位置
        this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
            onComplete();
        });
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _a, _b;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 为每个连锁格子播放消失动画
        floodFill.revealedBlocks.forEach(function (block, index) {
            // 延迟播放动画，创造连锁效果
            _this.scheduleOnce(function () {
                _this.chessBoardController.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
            }, index * 0.1); // 每个格子间隔0.1秒
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        if (!this.chessBoardController || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
        if (playerTotalScores) {
            this.displayPlayerScoreAnimationsAndUpdateTotalScores(playerActions, playerTotalScores);
        }
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.chessBoardController) {
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.chessBoardController) {
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.chessBoardController) {
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("ChessBoardController未设置，无法显示player_game_pfb加分效果");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            if (seconds > 0) {
                this.timeLabel.string = "" + seconds; // 显示纯数字：5, 4, 3, 2, 1
            }
            else {
                this.timeLabel.string = ""; // 不显示0，显示空白
            }
        }
        this.currentCountdown = seconds;
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else {
            console.warn("chessBoardController 不存在，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 先显示+1先手奖励
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, 1);
                console.log("\uD83C\uDFAF \u663E\u793A\u5148\u624B\u73A9\u5BB6 " + action.userId + " \u7684 +1 \u5148\u624B\u5956\u52B1");
            }
        }, 1.2);
        // 第二步：在棋盘头像上显示+1先手奖励（1.4秒）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, 1);
            console.log("\uD83C\uDFAF \u5728\u68CB\u76D8\u5934\u50CF\u4E0A\u663E\u793A\u5148\u624B\u73A9\u5BB6 " + action.userId + " \u7684 +1 \u5148\u624B\u5956\u52B1");
        }, 1.4);
        // 第三步：在分数面板显示本回合分数（2.4秒）
        this.scheduleOnce(function () {
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
                console.log("\uD83C\uDFAF \u663E\u793A\u5148\u624B\u73A9\u5BB6 " + action.userId + " \u7684\u672C\u56DE\u5408\u5206\u6570: " + action.score);
            }
        }, 2.4);
        // 第四步：在棋盘头像上显示本回合分数（2.6秒）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
            console.log("\uD83C\uDFAF \u5728\u68CB\u76D8\u5934\u50CF\u4E0A\u663E\u793A\u5148\u624B\u73A9\u5BB6 " + action.userId + " \u7684\u672C\u56DE\u5408\u5206\u6570: " + action.score);
        }, 2.6);
        // 第五步：更新总分（3.6秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
                console.log("\uD83C\uDFAF \u66F4\u65B0\u5148\u624B\u73A9\u5BB6 " + action.userId + " \u7684\u603B\u5206: " + totalScore);
            }
        }, 3.6);
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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