
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/ChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b2da8U/JnpOW6usOBaTL1QA', 'ChessBoardController');
// scripts/game/Chess/ChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChessBoardController = /** @class */ (function (_super) {
    __extends(ChessBoardController, _super);
    function ChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null; // player_game_pfb 预制体
        _this.boardNode = null; // 棋盘节点
        // 棋盘配置
        _this.BOARD_SIZE = 8; // 8x8棋盘
        _this.BOARD_WIDTH = 750; // 棋盘总宽度
        _this.BOARD_HEIGHT = 750; // 棋盘总高度
        _this.GRID_SIZE = 88; // 每个格子的大小 88x88
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 添加到坐标历史记录
        _this.coordinateHistory = [];
        // 自定义偏移量（如果需要调整位置）
        _this.customOffsetX = 0;
        _this.customOffsetY = -16; // 恢复原来的值，保持点击生成位置正确
        return _this;
        // update (dt) {}
    }
    ChessBoardController.prototype.onLoad = function () {
        this.initBoard();
    };
    ChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    // 初始化棋盘
    ChessBoardController.prototype.initBoard = function () {
        // 初始化数据数组
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    ChessBoardController.prototype.createGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    ChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    ChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标
    ChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        var x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 为格子节点设置触摸事件
    ChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C setupGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + x + "," + y + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    ChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    ChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1 // 1 = 挖掘
        });
    };
    // 格子长按事件 - 发送标记操作
    ChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2 // 2 = 标记
        });
    };
    // 在格子上放置玩家预制体
    ChessBoardController.prototype.placePlayerOnGrid = function (x, y, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C placePlayerOnGrid: \u65E0\u6548\u5750\u6807(" + x + "," + y + ")");
            return;
        }
        // 双重检查：确保格子为空
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error("\u274C placePlayerOnGrid: \u683C\u5B50(" + x + "," + y + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置
        var correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, function () {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 计算正确的位置（格子中心偏移(0, -16)）
    ChessBoardController.prototype.calculateCorrectPosition = function (x, y) {
        // 使用自定义偏移量
        var offsetX = this.customOffsetX;
        var offsetY = this.customOffsetY;
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    ChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        var position = cc.v2(finalX, finalY);
        return position;
    };
    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    ChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    ChessBoardController.prototype.playAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }
        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    ChessBoardController.prototype.calculateBasePositionByPlayerCount = function (x, y, totalPlayers) {
        var offsetX = this.customOffsetX;
        var offsetY;
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
        }
        else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
        }
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    ChessBoardController.prototype.calculateMultiPlayerBasePosition = function (x, y) {
        // 多人情况使用独立的偏移逻辑
        var offsetX = this.customOffsetX;
        var offsetY = 0; // 多人时不往下偏移，逻辑分开
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移（不包含往下偏移）
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    // 安全地添加玩家节点（处理Layout限制）
    ChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 方案1: 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    };
    // 备选方案：添加到父节点（Layout外部）
    ChessBoardController.prototype.addToParentNode = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        if (this.boardNode.parent) {
            // 需要转换坐标系
            var worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            var localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);
            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);
        }
        else {
            console.error("\u274C \u68CB\u76D8\u8282\u70B9\u6CA1\u6709\u7236\u8282\u70B9");
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    ChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, x, y, withFlag, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(function () {
                    }, 1.0);
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + x + "," + y + ")");
            }
            // 创建用户数据并设置头像
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    };
    // 设置玩家头像（保留原方法用于其他地方）
    ChessBoardController.prototype.setupPlayerAvatar = function (playerNode, x, y) {
        var _this = this;
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }
            // 创建用户数据
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 安全地调用setData
            try {
                playerController.setData(userData);
                // 延迟检查头像是否加载成功
                this.scheduleOnce(function () {
                    _this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);
            }
            catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");
            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    };
    // 检查头像是否加载成功
    ChessBoardController.prototype.checkAvatarLoaded = function (avatarNode, x, y) {
        if (!avatarNode) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u4E3Anull");
            return;
        }
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u6CA1\u6709Sprite\u7EC4\u4EF6");
            return;
        }
        if (!sprite.spriteFrame) {
            console.warn("\u26A0\uFE0F \u4F4D\u7F6E(" + x + "," + y + ")\u7684\u5934\u50CF\u53EF\u80FD\u52A0\u8F7D\u5931\u8D25\uFF0CspriteFrame\u4E3Anull");
            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
        }
    };
    // 设置备用头像（纯色方块）
    ChessBoardController.prototype.setFallbackAvatar = function (avatarNode, x, y) {
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var colors = [
            [255, 107, 107, 255],
            [78, 205, 196, 255],
            [69, 183, 209, 255],
            [150, 206, 180, 255],
            [255, 234, 167, 255] // 黄色
        ];
        var colorIndex = (x + y) % colors.length;
        var color = colors[colorIndex];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;
    };
    // 尝试直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetup = function (playerNode, x, y) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
        }
    };
    // 获取默认头像URL
    ChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    // 保存格子坐标（用于后续发送给后端）
    ChessBoardController.prototype.saveGridCoordinate = function (x, y) {
        // 这里可以将坐标保存到数组或发送给后端
        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);
        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    };
    // 发送坐标到服务器
    ChessBoardController.prototype.sendCoordinateToServer = function (x, y) {
        // 构造发送数据
        var moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };
        // 暂时只是打印，避免未使用变量警告
        return moveData;
    };
    ChessBoardController.prototype.addToCoordinateHistory = function (x, y) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });
    };
    // 获取当前玩家ID（示例）
    ChessBoardController.prototype.getCurrentPlayerId = function () {
        // 这里应该从全局状态或用户数据中获取
        return "player_001"; // 示例ID
    };
    // 获取指定坐标的格子数据
    ChessBoardController.prototype.getGridData = function (x, y) {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    };
    // 清除指定格子的玩家
    ChessBoardController.prototype.clearGridPlayer = function (x, y) {
        var gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    ChessBoardController.prototype.clearAllPlayers = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
    };
    // 获取所有已放置玩家的坐标
    ChessBoardController.prototype.getAllPlayerCoordinates = function () {
        var coordinates = [];
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({ x: x, y: y });
                }
            }
        }
        return coordinates;
    };
    // 检查坐标是否有效
    ChessBoardController.prototype.isValidCoordinate = function (x, y) {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    };
    // 检查格子是否为空
    ChessBoardController.prototype.isGridEmpty = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    };
    // 获取坐标历史记录
    ChessBoardController.prototype.getCoordinateHistory = function () {
        return __spreadArrays(this.coordinateHistory); // 返回副本
    };
    // 清除坐标历史记录
    ChessBoardController.prototype.clearCoordinateHistory = function () {
        this.coordinateHistory = [];
    };
    // 根据世界坐标获取格子坐标
    ChessBoardController.prototype.getGridCoordinateFromWorldPos = function (worldPos) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }
        // 将世界坐标转换为相对于棋盘的坐标
        var localPos = this.boardNode.convertToNodeSpaceAR(worldPos);
        // 计算格子坐标
        var x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 高亮显示格子（可选功能）
    ChessBoardController.prototype.highlightGrid = function (x, y, highlight) {
        if (highlight === void 0) { highlight = true; }
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
            }
            else {
                gridNode.color = cc.Color.WHITE;
            }
        }
    };
    // 批量放置玩家（用于从服务器同步数据）
    ChessBoardController.prototype.batchPlacePlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidCoordinate(coord.x, coord.y) && _this.isGridEmpty(coord.x, coord.y)) {
                _this.placePlayerOnGrid(coord.x, coord.y);
            }
        });
    };
    // 手动启用触摸事件（调试用）
    ChessBoardController.prototype.manualEnableTouch = function () {
        this.enableTouchForExistingGrids();
    };
    // 测试点击功能（调试用）
    ChessBoardController.prototype.testClick = function (x, y) {
        this.onGridClick(x, y);
    };
    // 获取棋盘状态信息（调试用）
    ChessBoardController.prototype.getBoardInfo = function () {
        var info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };
        return info;
    };
    // 简单测试方法 - 只测试位置，不加载头像
    ChessBoardController.prototype.simpleTest = function (x, y) {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }
        // 创建一个简单的彩色方块
        var testNode = new cc.Node("Test_" + x + "_" + y);
        // 添加一个彩色方块
        var sprite = testNode.addComponent(cc.Sprite);
        var texture = new cc.Texture2D();
        var color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        testNode.setContentSize(60, 60);
        // 计算位置
        var pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);
        // 添加坐标标签
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = "(" + x + "," + y + ")";
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);
        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);
    };
    // 清除所有测试节点
    ChessBoardController.prototype.clearTestNodes = function () {
        if (this.boardNode) {
            var children = this.boardNode.children.slice();
            children.forEach(function (child) {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
    };
    // 切换到父节点添加模式（如果Layout问题仍然存在）
    ChessBoardController.prototype.useParentNodeMode = function () {
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    };
    // 重新启用Layout（如果需要）
    ChessBoardController.prototype.reEnableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;
        }
    };
    // 永久禁用Layout
    ChessBoardController.prototype.disableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;
        }
    };
    // 设置自定义偏移量
    ChessBoardController.prototype.setCustomOffset = function (offsetX, offsetY) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
    };
    // 获取当前偏移量
    ChessBoardController.prototype.getCurrentOffset = function () {
        return { x: this.customOffsetX, y: this.customOffsetY };
    };
    // 测试不同偏移量
    ChessBoardController.prototype.testWithOffset = function (x, y, offsetX, offsetY) {
        // 临时保存当前偏移
        var originalOffsetX = this.customOffsetX;
        var originalOffsetY = this.customOffsetY;
        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);
        // 执行测试
        this.simpleTest(x, y);
        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    };
    // 测试头像显示功能
    ChessBoardController.prototype.testAvatarDisplay = function (x, y) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }
        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }
        // 直接调用放置玩家方法
        this.placePlayerOnGrid(x, y);
        // 延迟检查结果
        this.scheduleOnce(function () {
            var gridData = _this.gridData[x][y];
            if (gridData.playerNode) {
                // 检查PlayerGameController
                var controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                    var sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                    }
                    else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                }
                else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            }
            else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    };
    // 调试预制体结构
    ChessBoardController.prototype.debugPrefabStructure = function () {
        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }
        // 实例化一个临时节点来检查结构
        var tempNode = cc.instantiate(this.playerGamePrefab);
        // 检查组件
        var controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
            if (controller.avatar) {
                var sprite = controller.avatar.getComponent(cc.Sprite);
            }
            else {
                console.error("❌ avatar节点不存在");
            }
        }
        else {
            console.error("❌ 找不到PlayerGameController组件");
        }
        // 列出所有子节点
        this.logNodeHierarchy(tempNode, 0);
        // 清理临时节点
        tempNode.destroy();
    };
    // 递归打印节点层级
    ChessBoardController.prototype.logNodeHierarchy = function (node, depth) {
        var indent = "  ".repeat(depth);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.logNodeHierarchy(child, depth + 1);
        }
    };
    // 异步加载头像
    ChessBoardController.prototype.loadAvatarAsync = function (avatarNode, url, onComplete) {
        var _this = this;
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }
        var avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }
        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u5934\u50CF\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 设置备用头像
                _this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }
            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;
            onComplete();
        });
    };
    // 异步直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetupAsync = function (playerNode, x, y, onComplete) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
            onComplete();
        }
    };
    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    ChessBoardController.prototype.showPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID
     */
    ChessBoardController.prototype.getCurrentUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForCurrentUser = function (score) {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    };
    /**
     * 为其他用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForOtherUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点
     */
    ChessBoardController.prototype.findPlayerNodeByRecentAction = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) { // 为前5个节点或匹配的节点输出组件列表
                var allComponents = child.getComponents(cc.Component);
            }
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果
     */
    ChessBoardController.prototype.showScoreOnPlayerController = function (playerController, score) {
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
    };
    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    ChessBoardController.prototype.showPlayerGameSubScore = function (userId, subScore) {
        var foundPlayer = false;
        // 遍历所有格子，查找玩家节点
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                        break;
                    }
                }
            }
            if (foundPlayer)
                break;
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u8282\u70B9\u6765\u663E\u793A\u51CF\u5206\u6548\u679C: userId=" + userId);
        }
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    ChessBoardController.prototype.resetGameScene = function () {
        console.log("🔄 开始重置游戏场景...");
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllGrids();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        console.log("✅ 游戏场景重置完成");
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    ChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        var totalChildren = this.boardNode.children.length;
        console.log("\uD83D\uDD0D \u5F00\u59CB\u68C0\u67E5 " + totalChildren + " \u4E2A\u5B50\u8282\u70B9");
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            console.log("\uD83D\uDD0D \u68C0\u67E5\u8282\u70B9 [" + i + "]: " + nodeName);
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
                console.log("\u2705 \u6807\u8BB0\u6E05\u9664: " + nodeName);
            }
            else {
                console.log("\u23ED\uFE0F \u4FDD\u7559\u8282\u70B9: " + nodeName);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child, index) {
            console.log("\uD83D\uDDD1\uFE0F \u6B63\u5728\u79FB\u9664 [" + index + "]: " + child.name);
            child.removeFromParent();
        });
        console.log("\uD83E\uDDF9 \u6E05\u9664\u4E86 " + childrenToRemove.length + " \u4E2A\u6E38\u620F\u5143\u7D20\uFF0C\u5269\u4F59 " + this.boardNode.children.length + " \u4E2A\u8282\u70B9");
        // 额外的强制清理：清理所有非Grid_开头的节点
        this.forceCleanNonGridNodes();
    };
    /**
     * 强制清理所有非Grid_开头的节点
     */
    ChessBoardController.prototype.forceCleanNonGridNodes = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 再次遍历，强制清除所有非Grid_开头的节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 只保留Grid_开头的节点
            if (!nodeName.startsWith("Grid_")) {
                childrenToRemove.push(child);
                console.log("\uD83D\uDD25 \u5F3A\u5236\u6E05\u9664: " + nodeName);
            }
        }
        // 移除找到的节点
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
        if (childrenToRemove.length > 0) {
            console.log("\uD83D\uDD25 \u5F3A\u5236\u6E05\u9664\u4E86\u989D\u5916\u7684 " + childrenToRemove.length + " \u4E2A\u975E\u683C\u5B50\u8282\u70B9");
        }
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    ChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 原始格子不清除（以Grid_开头的节点）
        if (nodeName.startsWith("Grid_")) {
            return false;
        }
        // 玩家预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 炸弹预制体
        if (nodeName === "Boom" || nodeName.includes("boom")) {
            return true;
        }
        // 数字预制体（各种可能的命名格式）
        if (nodeName.match(/^Boom\d+$/) || nodeName.match(/^\d+boom$/) || nodeName.match(/^\d+$/)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag")) {
            return true;
        }
        // 测试节点
        if (nodeName.startsWith("Test_")) {
            return true;
        }
        // 临时节点
        if (nodeName.includes("Temp") || nodeName.includes("temp")) {
            return true;
        }
        // 通过组件判断是否是玩家预制体
        if (node.getComponent("PlayerGameController")) {
            return true;
        }
        // 检查是否有其他游戏相关组件
        var components = node.getComponents(cc.Component);
        for (var _i = 0, components_1 = components; _i < components_1.length; _i++) {
            var comp = components_1[_i];
            var compName = comp.constructor.name;
            if (compName.includes("Player") || compName.includes("Game") || compName.includes("Mine")) {
                return true;
            }
        }
        return false;
    };
    /**
     * 显示所有小格子
     */
    ChessBoardController.prototype.showAllGrids = function () {
        if (!this.boardNode) {
            return;
        }
        var shownCount = 0;
        var restoredCount = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_")) {
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                shownCount++;
                // 如果之前是隐藏状态，记录恢复数量
                if (!child.active || child.opacity < 255) {
                    restoredCount++;
                }
            }
        }
        console.log("\uD83D\uDCCB \u663E\u793A\u4E86 " + shownCount + " \u4E2A\u5C0F\u683C\u5B50\uFF0C\u6062\u590D\u4E86 " + restoredCount + " \u4E2A\u9690\u85CF\u7684\u683C\u5B50");
    };
    /**
     * 隐藏指定位置的小格子（点击时调用）
     */
    ChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 重新初始化棋盘数据
     */
    ChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置gridData中的玩家状态
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
        // 清除坐标历史记录
        this.clearCoordinateHistory();
        console.log("📊 棋盘数据重新初始化完成");
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    ChessBoardController.prototype.clearAllPlayerNodes = function () {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }
        var totalCleared = 0;
        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                }
            }
        }
        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
            }
        }
        // 移除找到的玩家预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    ChessBoardController.prototype.displayOtherPlayersAtPosition = function (x, y, actions) {
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + x + ", " + y + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            }
            else {
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    };
    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToExistingGrid = function (x, y, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.adjustMyAvatarPosition = function (x, y, position, actions) {
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = this.gridData[x][y].playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 根据总人数计算基础位置
        var basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToEmptyGrid = function (x, y, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    ChessBoardController.prototype.createOtherPlayerAtBoardPosition = function (gridX, gridY, action, relativePosition, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 根据总人数计算基础位置（统一逻辑）
        var basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);
        // 添加相对偏移
        var finalPosition = cc.v2(basePosition.x + relativePosition.x, basePosition.y + relativePosition.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, function () {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                _this.playAvatarSpawnAnimation(playerNode);
            }
            else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
        });
    };
    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.setupOtherPlayerData = function (playerNode, action, onComplete) {
        try {
            var playerController_1 = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController_1) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData_1 = this.getRealUserData(action.userId);
            if (!realUserData_1) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(function () {
                if (typeof playerController_1.setData === 'function') {
                    playerController_1.setData(realUserData_1);
                }
                // 根据操作类型设置旗子显示
                var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController_1.flagNode) {
                    playerController_1.flagNode.active = withFlag;
                }
                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u5176\u4ED6\u73A9\u5BB6\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    ChessBoardController.prototype.getPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getPlayerPositions(4);
        }
    };
    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    ChessBoardController.prototype.getGridNode = function (x, y) {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }
        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        var index = y * this.BOARD_SIZE + x;
        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }
        return null;
    };
    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    ChessBoardController.prototype.createPlayerNodeAtPosition = function (gridNode, action, position) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 检查预制体上的组件
        var components = playerNode.getComponents(cc.Component);
        components.forEach(function (comp, index) {
        });
        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
        // 添加到格子节点
        gridNode.addChild(playerNode);
        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);
    };
    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    ChessBoardController.prototype.setupPlayerNodeData = function (playerNode, action) {
        try {
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                var allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach(function (comp, index) {
                });
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }
            // 根据操作类型设置旗子显示
            var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }
            else {
                console.warn("找不到flagNode节点");
            }
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u73A9\u5BB6\u8282\u70B9\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    ChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    ChessBoardController.prototype.showScoreOnPlayerNode = function (x, y, score, showPlusOne) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
            return;
        }
        // 查找该位置的玩家节点
        var playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    };
    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    ChessBoardController.prototype.findPlayerNodeAtPosition = function (x, y) {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 计算该位置的世界坐标
        var targetPosition = this.calculateCorrectPosition(x, y);
        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        var closestNode = null;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                var distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }
        return closestNode;
    };
    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.showScoreAnimationOnNode = function (playerController, score, onComplete) {
        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.hideAvatarsAtPosition = function (x, y, onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (var gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (var gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                }
            }
        }
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        if (avatarNodes.length === 0) {
            // 没有头像需要消失
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画
        avatarNodes.forEach(function (avatarNode, index) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                    _this.clearAllMyAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    ChessBoardController.prototype.clearAllMyAvatarReferences = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用cc.Tween播放消失动画后隐藏（不销毁）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    ChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    };
    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.loadNumberPrefab = function (x, y, number) {
        var prefabName = number + "boom";
        this.createTemporaryNumberNode(x, y, number);
    };
    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createTemporaryNumberNode = function (x, y, number) {
        // 创建数字显示节点
        var numberNode = new cc.Node("NeighborMines_" + number);
        var label = numberNode.addComponent(cc.Label);
        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.setupNumberNode = function (numberNode, x, y, number) {
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    ChessBoardController.prototype.playNumberAppearAnimation = function (numberNode, number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    };
    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    ChessBoardController.prototype.getNumberColor = function (number) {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    };
    /**
     * 播放棋盘震动动画
     */
    ChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 震动参数 - 增强震动效果
        var shakeIntensity = 25; // 震动强度（从10增加到25）
        var shakeDuration = 0.8; // 震动持续时间（从0.5增加到0.8）
        var shakeFrequency = 30; // 震动频率（从20增加到30，更快的震动）
        // 创建更强烈的震动动画，使用递减强度
        var currentIntensity = shakeIntensity;
        var intensityDecay = 0.9; // 强度衰减系数
        var createShakeStep = function (intensity) {
            return cc.tween()
                .to(0.02, {
                x: originalPosition.x + (Math.random() - 0.5) * intensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * intensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(shakeDuration * shakeFrequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.15, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], ChessBoardController.prototype, "boardNode", void 0);
    ChessBoardController = __decorate([
        ccclass
    ], ChessBoardController);
    return ChessBoardController;
}(cc.Component));
exports.default = ChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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