
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/bean/GameBean.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8d8d2RWlP1Coqs84m843tJg', 'GameBean');
// scripts/bean/GameBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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