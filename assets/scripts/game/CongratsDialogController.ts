// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html


import { NoticeSettlement } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { EventType } from "../common/EventCenter";
import { GameMgr } from "../common/GameMgr";
import { AutoMessageBean, AutoMessageId } from "../net/MessageBaseBean";
import CongratsItemController from "../pfb/CongratsItemController";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

//结算页面
@ccclass
export default class CongratsDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null
    @property(cc.Node)
    backBtn: cc.Node = null
    @property(cc.Prefab)
    congratsItem: cc.Prefab = null;//列表的 item
    @property(cc.Node)
    public layoutNode: cc.Node = null;//存放列表的布局


    countdownTimeLabel: cc.Label = null
    countdownInterval: number = null;//倒计时的 id

    backCallback: Function = null //隐藏弹窗的回调
    seconds: number = 10;//倒计时 10 秒


    onLoad() {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    }
    protected onEnable(): void {
        this.updateCountdownLabel(this.seconds);
        Tools.setCountDownTimeLabel(this.backBtn)
    }

    start() {
        //backBtn 按钮点击事件
        Tools.greenButton(this.backBtn, () => {
            this.hide(true)
        })
    }


    show(noticeSettlement: NoticeSettlement, backCallback: Function) {
        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0
        this._setData(noticeSettlement)
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }

    _setData(noticeSettlement: NoticeSettlement) {
        // 检查 users 数组是否存在
        if (!noticeSettlement || !noticeSettlement.users || !Array.isArray(noticeSettlement.users)) {
            console.warn('NoticeSettlement users 数据无效:', noticeSettlement);
            this.startCountdown(10); // 仍然启动倒计时
            return;
        }

        const index = noticeSettlement.users.findIndex((item) => item.userId === GlobalBean.GetInstance().loginData.userInfo.userId);//搜索
        if (index >= 0) {
            GlobalBean.GetInstance().loginData.userInfo.coin = noticeSettlement.users[index].coin //更新自己的最新金币
        }

        this.layoutNode.removeAllChildren();
        for (let i = 0; i < noticeSettlement.users.length; ++i) {
            const item = cc.instantiate(this.congratsItem);
            const data = noticeSettlement.users[i];
            this.layoutNode.addChild(item);
            setTimeout(() => {
                item.getComponent(CongratsItemController).createData(data, GlobalBean.GetInstance().noticeStartGame.users);
            }, 100);
        }
        this.startCountdown(10)//倒计时 10 秒
    }

    // bool 在隐藏的时候是否返回大厅
    hide(bool: boolean = false) {
        if (this.backCallback) {
            this.backCallback()
        }
        GameMgr.Console.Log('隐藏结算页面')
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
                if (bool) {
                    GlobalBean.GetInstance().cleanData()
                    let autoMessageBean: AutoMessageBean = {
                        'msgId': AutoMessageId.JumpHallPage,//跳转进大厅页面
                        'data': { 'type': 2 }//2是结算弹窗跳转的
                    }
                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
                }
            })
            .start();
    }
    protected onDisable(): void {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }

    }

    startCountdown(seconds: number) {
        let remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);

        this.countdownInterval = setInterval(() => {
            remainingSeconds--;

            if (remainingSeconds <= 0) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null
                // 倒计时结束时的处理逻辑
                this.hide(true)
                return
            }
            this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    }

    updateCountdownLabel(seconds: number) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = `（${seconds}s）`;
        }
    }
}
