


import { Publish } from "../../meshTools/tools/Publish";
import { RoomUser, UserSettlement, PlayerFinalResult } from "../bean/GameBean";
import { Config } from "../util/Config";
import NickNameLabel from "../util/NickNameLabel";
import { Tools } from "../util/Tools";

const { ccclass } = cc._decorator;

@ccclass
export default class CongratsItemController extends cc.Component {

    boardIconCrownNode: cc.Node;
    boardNum: cc.Node;
    avatarNode: cc.Node;
    nameNode: cc.Node;
    stepNode: cc.Node;
    numberNode: cc.Node;
    beanIcon: cc.Node;


    protected start(): void {
        this.boardIconCrownNode = this.node.getChildByName('board_icon_crown');//皇冠的节点
        this.boardNum = this.node.getChildByName('board_num');//皇冠的节点
        this.avatarNode = this.node.getChildByName('avatar');//头像的节点
        this.nameNode = this.node.getChildByName('name_layout').getChildByName('name');//名称的节点
        this.stepNode = this.node.getChildByName(`step_layout`).getChildByName('step');// 步数的节点
        this.numberNode = this.node.getChildByName('congrats_list_frame').getChildByName('number_view').getChildByName('number');//金豆的节点
        this.beanIcon = this.node.getChildByName('congrats_list_frame').getChildByName('board_icon_beans')//金豆图标

    }

    //设置数据
    //settleType:结算类型
    //intUserID:中断游戏的玩家Id
    createData(settlement: UserSettlement | PlayerFinalResult, gameUsers: RoomUser[]) {

        if (settlement.rank <= 3) {
            this.boardIconCrownNode.active = true
            this.boardNum.active = false
            //设置皇冠图片
            switch (settlement.rank) {
                case 1:
                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_01');
                    break;
                case 2:
                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_02');
                    break;
                case 3:
                    Tools.setNodeSpriteFrame(this.boardIconCrownNode, Config.gameRes + 'board_icon_crown_03');
                    break;
            }
        } else {
            this.boardIconCrownNode.active = false
            this.boardNum.active = true
            this.boardNum.getComponent(cc.Label).string = settlement.rank + '';//显示名次
        }

        // 根据数据类型显示不同的分数字段
        let scoreValue: number;
        if ('score' in settlement) {
            // UserSettlement 类型
            scoreValue = settlement.score;
        } else {
            // PlayerFinalResult 类型
            scoreValue = settlement.totalScore;
        }
        this.stepNode.getComponent(cc.Label).string = scoreValue + '';//显示分数

        const index = gameUsers.findIndex((item) => item.userId === settlement.userId);//搜索
        if (index != -1) {
            let user = gameUsers[index];
            Tools.setNodeSpriteFrameUrl(this.avatarNode, user.avatar);
            this.nameNode.getComponent(NickNameLabel).string = user.nickName

        }
        this.numberNode.getComponent(cc.Label).string = Tools.NumToTBMK(settlement.coinChg)


        if (Publish.GetInstance().currencyIcon != null && Publish.GetInstance().currencyIcon !== '') {
            Tools.setNodeSpriteFrameUrl(this.beanIcon, Publish.GetInstance().currencyIcon)
        }

    }
}
